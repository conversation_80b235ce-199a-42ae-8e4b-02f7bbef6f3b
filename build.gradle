group 'oikos'
version '1.3'

apply plugin: 'java'
apply plugin: 'application'

mainClassName = 'ar.com.oikosanalistas.admin.MainLauncher'

sourceCompatibility = 1.8

repositories {
    mavenCentral()
}

dependencies {
    implementation 'com.google.firebase:firebase-admin:7.1.1'
    implementation 'com.google.guava:guava:25.0-jre'
    implementation 'com.intellij:forms_rt:7.0.3'
    implementation 'org.greenrobot:eventbus:3.1.1'
    implementation 'org.apache.poi:poi-ooxml:5.0.0'
    implementation 'com.github.lgooddatepicker:LGoodDatePicker:10.3.1'
    implementation 'org.slf4j:slf4j-simple:1.7.30'
    testImplementation group: 'junit', name: 'junit', version: '4.12'
}