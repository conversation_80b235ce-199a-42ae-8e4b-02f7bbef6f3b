package ar.com.oikosanalistas.admin.file;

import ar.com.oikosanalistas.admin.ui.model.GenerarResultadosModel;
import ar.com.oikosanalistas.admin.ui.model.pojo.EncuestaRealizada;
import ar.com.oikosanalistas.admin.ui.view.BaseFrame;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.Hyperlink;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.annotation.Nullable;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static ar.com.oikosanalistas.admin.utils.Constants.*;
import io.sentry.Sentry;

public class EncuestaFileCreator extends FileCreator {

    private String filename;
    private List<EncuestaRealizada> encuestasRealizadas;
    private String tituloEncuesta;
    private GenerarResultadosModel resultadosModel;

    public EncuestaFileCreator(String filename, List<EncuestaRealizada> encuestasRealizadas, String tituloEncuesta, GenerarResultadosModel resultadosModel) {
        this.filename = filename;
        this.encuestasRealizadas = encuestasRealizadas;
        this.tituloEncuesta = tituloEncuesta;
        this.resultadosModel = resultadosModel;
    }

    @Override
    public void createFile(@Nullable BaseFrame baseFrame) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet(tituloEncuesta);
        //Style for the cells
        XSSFCellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        //Main container for all the cells list.
        List<List<String>> listContainer = new ArrayList<>();

        //Extract the questions
        listContainer.add(resultadosModel.getPreguntasStringList());

        //Process the answers and add them to the container
        for (EncuestaRealizada encuestaRealizada : encuestasRealizadas) {
            List<String> listaRespuestas = resultadosModel.getRespuestas(encuestaRealizada.getMapPreguntaRespuesta(), encuestaRealizada);
            listContainer.add(listaRespuestas);
        }

        int filaNum = 0;
        for (List<String> listaColumnas : listContainer) {
            Row fila = sheet.createRow(filaNum++);
            int colNum = 0;

            for (String contenidoFila : listaColumnas) {
                sheet.autoSizeColumn(colNum);
                Cell celda = fila.createCell(colNum);
                celda.setCellValue(contenidoFila);
                if (filaNum == 1) {
                    celda.setCellStyle(style);
                }

                if ((contenidoFila != null) && (contenidoFila.startsWith(GOOGLE_MAPS_LINK))) {
                    CreationHelper createHelper = workbook.getCreationHelper();
                    Hyperlink link = createHelper.createHyperlink(HyperlinkType.URL);

                    link.setAddress(contenidoFila);
                    celda.setHyperlink(link);
                }
                colNum++;
            }
        }

        try {
            FileOutputStream outputStream = new FileOutputStream(getFilename());
            workbook.write(outputStream);
            workbook.close();

            System.out.println(getFileCreationSuccessLogMessage());

            conditionallyOpenCreatedFile();
        } catch (IOException e) {
            Sentry.captureException(e);
            e.printStackTrace();
            if (baseFrame != null) {
                baseFrame.showMessageDialog(getFileCreationErrorMessage());
            }
        }
    }

    @Override
    protected String getFilename() {
        return filename;
    }

    @Override
    protected String getFilenameAlreadyExistsMessage() {
        return ENCUESTA_FILE_ALREADY_EXISTS_MESSAGE;
    }

    @Override
    protected String getFileCreationSuccessLogMessage() {
        return ENCUESTA_FILE_CREATE_SUCCESS;
    }

    @Override
    protected String getFileCreationErrorMessage() {
        return SAVE_ENCUESTAS_ERROR_MESSAGE;
    }

    @Override
    protected boolean openFileOnCreationEnd() {
        return OPEN_RESULTADOS_ENCUESTA_ON_CREATION_END;
    }

    @Override
    protected String getFileContent() {
        return null;
    }
}
