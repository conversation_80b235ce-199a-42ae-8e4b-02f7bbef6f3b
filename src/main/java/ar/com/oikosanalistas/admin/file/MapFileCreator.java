package ar.com.oikosanalistas.admin.file;

import static ar.com.oikosanalistas.admin.utils.Constants.*;

public class MapFileCreator extends FileCreator {
    private String filename;
    private String locationsList;
    private String htmlPart1;
    private String htmlPart2;

    public MapFileCreator(String filename, String locationsList, String htmlPart1, String htmlPart2) {
        this.filename = filename;
        this.locationsList = locationsList;
        this.htmlPart1 = htmlPart1;
        this.htmlPart2 = htmlPart2;
    }

    @Override
    protected String getFilename() {
        return filename;
    }

    @Override
    protected String getFilenameAlreadyExistsMessage() {
        return MAP_FILE_ALREADY_EXISTS_MESSAGE;
    }

    @Override
    protected String getFileCreationSuccessLogMessage() {
        return MAP_FILE_CREATE_SUCCESS;
    }

    @Override
    protected String getFileCreationErrorMessage() {
        return SAVE_MAP_ERROR_MESSAGE;
    }

    @Override
    protected boolean openFileOnCreationEnd() {
        return OPEN_MAP_ON_CREATION_END;
    }

    @Override
    protected String getFileContent() {
        return htmlPart1 +
                locationsList +
                htmlPart2;
    }
}
