package ar.com.oikosanalistas.admin.file;

import ar.com.oikosanalistas.admin.ui.view.BaseFrame;

import javax.annotation.Nullable;
import javax.swing.*;
import java.awt.*;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

import static ar.com.oikosanalistas.admin.utils.Constants.SAVE_FILE_BUTTON_TEXT;
import io.sentry.Sentry;

public abstract class FileCreator {

    public static String promptForFolder(String extension, String title, Component parent) {
        JFileChooser fc = new JFileChooser();
        fc.setDialogTitle(title);
        fc.setApproveButtonText(SAVE_FILE_BUTTON_TEXT);

        if (fc.showOpenDialog(parent) == JFileChooser.APPROVE_OPTION) {
            String fileName = fc.getSelectedFile().getName();
            if (!fileName.toLowerCase().endsWith(extension)) {
                return fc.getSelectedFile().getAbsolutePath() + extension;
            } else {
                return fileName;
            }
        }

        System.out.println("Canceled op");

        return null;
    }

    public void createFile(@Nullable BaseFrame baseFrame) {
        File file = new File(getFilename());
        if (file.exists()) {
            System.out.println("File " + getFilename() + " already exists.");
            if (baseFrame != null) {
                baseFrame.showMessageDialog(getFilenameAlreadyExistsMessage());
            }
        } else {
            FileWriter fileWriter = null;
            BufferedWriter bufferedWriter = null;
            try {
                fileWriter = new FileWriter(file);
                bufferedWriter = new BufferedWriter(fileWriter);

                bufferedWriter.write(getFileContent());
                bufferedWriter.flush();
                fileWriter.flush();

                System.out.println(getFileCreationSuccessLogMessage());
            } catch (IOException e) {
                Sentry.captureException(e);
                e.printStackTrace();
                if (baseFrame != null) {
                    baseFrame.showMessageDialog(getFileCreationErrorMessage());
                }
            } finally {
                try {
                    if (bufferedWriter != null) {
                        bufferedWriter.close();
                    }

                    if (fileWriter != null) {
                        fileWriter.close();
                    }

                    conditionallyOpenCreatedFile();
                } catch (IOException e) {
                    Sentry.captureException(e);
                    e.printStackTrace();
                }
            }
        }
    }

    protected void conditionallyOpenCreatedFile() throws IOException {
        if (openFileOnCreationEnd()) {
            openCreatedFile();
        }
    }

    private void openCreatedFile() throws IOException {
        File file = new File(getFilename());
        Desktop.getDesktop().open(file.getParentFile());
    }

    protected abstract String getFilename();

    protected abstract String getFilenameAlreadyExistsMessage();

    protected abstract String getFileCreationSuccessLogMessage();

    protected abstract String getFileCreationErrorMessage();

    protected abstract boolean openFileOnCreationEnd();

    protected abstract String getFileContent();
}
