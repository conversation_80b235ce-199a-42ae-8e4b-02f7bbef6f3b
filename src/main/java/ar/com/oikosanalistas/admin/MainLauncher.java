package ar.com.oikosanalistas.admin;

import ar.com.oikosanalistas.admin.ui.controller.MainFrameController;
import ar.com.oikosanalistas.admin.utils.Constants;
import com.formdev.flatlaf.FlatLightLaf;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;

import java.io.FileInputStream;
import java.io.IOException;

import static ar.com.oikosanalistas.admin.utils.Constants.GOOGLE_CLOUD_CONFIG_FILENAME;
import static ar.com.oikosanalistas.admin.utils.Constants.GOOGLE_CLOUD_CONFIG_TEST_FILENAME;

public class MainLauncher {

    public static void main(String[] args) {
        FlatLightLaf.setup();
        initializeFirebase();

        MainFrameController mainFrameController = new MainFrameController();
        mainFrameController.showMainFrameWindow();
    }

    private static void initializeFirebase() {
        initializeProd();
//        initializeTest();
    }

    private static void initializeProd() {
        try {
            FileInputStream serviceAccount = new FileInputStream(GOOGLE_CLOUD_CONFIG_FILENAME);
            FirebaseOptions options = FirebaseOptions.builder()
                    .setCredentials(GoogleCredentials.fromStream(serviceAccount))
                    .setDatabaseUrl(Constants.FIREBASE_DATABASE_URL)
                    .build();

            FirebaseApp.initializeApp(options);
        } catch (IOException e) {
            System.out.println("ERROR: invalid service account credentials. See README.");
            System.out.println(e.getMessage());

            System.exit(1);
        }
    }

    private static void initializeTest() {
        try {
            FileInputStream serviceAccount = new FileInputStream(GOOGLE_CLOUD_CONFIG_TEST_FILENAME);
            FirebaseOptions options = FirebaseOptions.builder()
                    .setCredentials(GoogleCredentials.fromStream(serviceAccount))
                    .setDatabaseUrl(Constants.FIREBASE_DATABASE_URL_TEST)
                    .build();

            FirebaseApp.initializeApp(options);
        } catch (IOException e) {
            System.out.println("ERROR: invalid service account credentials. See README.");
            System.out.println(e.getMessage());

            System.exit(1);
        }
    }
}
