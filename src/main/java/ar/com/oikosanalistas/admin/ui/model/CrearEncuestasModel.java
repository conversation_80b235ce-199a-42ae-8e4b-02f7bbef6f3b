package ar.com.oikosanalistas.admin.ui.model;

import ar.com.oikosanalistas.admin.events.CreateEncuestaEvent;
import ar.com.oikosanalistas.admin.events.EditarEncuestaEvent;
import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import ar.com.oikosanalistas.admin.ui.model.pojo.pregunta.Pregunta;
import ar.com.oikosanalistas.admin.ui.model.pojo.respuesta.Respuesta;
import ar.com.oikosanalistas.admin.ui.view.viewmodel.*;
import ar.com.oikosanalistas.admin.utils.Constants;
import com.google.api.core.ApiFutureCallback;
import com.google.api.core.ApiFutures;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static ar.com.oikosanalistas.admin.utils.Constants.*;
import static com.google.common.util.concurrent.MoreExecutors.directExecutor;

public class CrearEncuestasModel extends BaseModel {

    private static final int TIPO_PREGUNTA_PRESELECCIONADO = 0;
    private Encuesta encuesta;

    private List<Respuesta> backUp;

    public CrearEncuestasModel() {
        this(new Encuesta());
    }

    public CrearEncuestasModel(Encuesta encuesta) {
        this.encuesta = encuesta;
    }

    public static String[] getTiposPregunta() {
        return TIPOS_PREGUNTA;
    }

    public static String getTipoPreguntaPreseleccionado() {
        return getTiposPregunta()[TIPO_PREGUNTA_PRESELECCIONADO];
    }

    public void resetEncuesta() {
        this.encuesta = new Encuesta();
    }

    public void setTitulo(String titulo) {
        encuesta.setTitulo(titulo);
    }

    public void setDescripcion(String descripcion) {
        encuesta.setDescripcion(descripcion);
    }

    public void setFechaInicio(String fechaInicio) {
        encuesta.setFechaInicio(fechaInicio);
    }

    public void setFechaFin(String fechaFin) {
        encuesta.setFechaFin(fechaFin);
    }

    public List<Pregunta> getListaPreguntas() {
        return encuesta.getPreguntasList();
    }

    /**
     * Se encarga de agregar una pregunta categorica a partir de la data cruda contenida en el viewmodel que recibe.
     *
     * @param viewModel Objeto con la data necesaria para crear la nueva pregunta categorica.
     */
    public void agregarPreguntaCategorica(NuevaPreguntaCategoricaViewModel viewModel) {
        Pregunta preguntaCategorica = new Pregunta();
        preguntaCategorica.setTipo(CATEGORICA);
        preguntaCategorica.setDescripcion(viewModel.getDescripcion());

        List<String> respuestas = viewModel.getRespuestas();
        for (int i = 0; i < respuestas.size(); i++) {
            String respuesta = respuestas.get(i);
            Respuesta respuestaCategorica = new Respuesta();
            respuestaCategorica.setDescripcion(respuesta);
            respuestaCategorica.setId(i);
            preguntaCategorica.addPosibleRespuestaCategorica(respuestaCategorica);
        }

        encuesta.agregarPregunta(preguntaCategorica);
    }

    public void agregarPreguntaNumerica(NuevaPreguntaNumericaViewModel viewModel) {
        Pregunta preguntaNumerica = new Pregunta();
        preguntaNumerica.setTipo(NUMERICA);
        preguntaNumerica.setMin(viewModel.getMinValue());
        preguntaNumerica.setMax(viewModel.getMaxValue());
        preguntaNumerica.setDescripcion(viewModel.getDescripcion());
        encuesta.agregarPregunta(preguntaNumerica);
    }

    public void agregarPreguntaTexto(NuevaPreguntaTextoViewModel viewModel) {
        Pregunta preguntaTexto = new Pregunta();
        preguntaTexto.setTipo(TEXTO);
        preguntaTexto.setDescripcion(viewModel.getDescripcion());
        encuesta.agregarPregunta(preguntaTexto);
    }

    public void agregarPreguntaMultiple(NuevaPreguntaMultipleViewModel viewModel) {
        Pregunta preguntaMultiple = new Pregunta();
        preguntaMultiple.setTipo(MULTIPLE);
        preguntaMultiple.setDescripcion(viewModel.getDescripcion());

        List<String> respuestas = viewModel.getRespuestas();
        for (int i = 0; i < respuestas.size(); i++) {
            String respuesta = respuestas.get(i);
            Respuesta respuestaMultiple = new Respuesta();
            respuestaMultiple.setDescripcion(respuesta);
            respuestaMultiple.setId(i);
            preguntaMultiple.addPosibleRespuestaCategorica(respuestaMultiple);
        }

        encuesta.agregarPregunta(preguntaMultiple);
    }

    public void agregarPreguntaMixta(NuevaPreguntaMixtaViewModel viewModel) {
        Pregunta preguntaMixta = new Pregunta();
        preguntaMixta.setTipo(MIXTA);
        preguntaMixta.setDescripcion(viewModel.getDescripcion());

        List<String> respuestas = viewModel.getRespuestas();
        for (int i = 0; i < respuestas.size(); i++) {
            String respuesta = respuestas.get(i);
            Respuesta respuestaMixta = new Respuesta();
            respuestaMixta.setDescripcion(respuesta);
            respuestaMixta.setId(i);
            preguntaMixta.addPosibleRespuestaCategorica(respuestaMixta);
        }

        Respuesta respuestaMixtaDefault = new Respuesta();
        respuestaMixtaDefault.setDescripcion(RESPUESTA_MIXTA_DEFAULT);
        respuestaMixtaDefault.setId(preguntaMixta.getCantidadPosiblesRespuestasCategoricas());
        preguntaMixta.addPosibleRespuestaCategorica(respuestaMixtaDefault);

        encuesta.agregarPregunta(preguntaMixta);
    }

    public void actualizarDescripcionPregunta(int position, String nuevaDescripcion) {
        Pregunta pregunta = encuesta.getPreguntasList().get(position);
        pregunta.setDescripcion(nuevaDescripcion);
    }

    public void actualizarDescripcionRespuesta(int positionPregunta, int positionRespuesta, String nuevaDescripcion) {
        Pregunta pregunta = encuesta.getPreguntasList().get(positionPregunta);
        pregunta.getPosiblesRespuestasCategoricas().get(positionRespuesta).setDescripcion(nuevaDescripcion);
    }

    public void actualizarValoresPreguntaNumerica(int position, int min, int max) {
        Pregunta pregunta = encuesta.getPreguntasList().get(position);
        pregunta.setMin(min);
        pregunta.setMax(max);
    }

    public void eliminarPregunta(int position) {
        encuesta.getPreguntasList().remove(position);
    }

    public void agregarRespuesta(int positionPregunta, String nuevaRespuesta) {
        Pregunta pregunta = getListaPreguntas().get(positionPregunta);
        Respuesta respuesta = new Respuesta();
        respuesta.setDescripcion(nuevaRespuesta);

        if (MIXTA.equals(pregunta.getTipo())) {
            int insertionPoint = Math.max(0, pregunta.getPosiblesRespuestasCategoricas().size() - 1);
            pregunta.getPosiblesRespuestasCategoricas().add(insertionPoint, respuesta);
        } else {
            pregunta.addPosibleRespuestaCategorica(respuesta);
        }
        actualizarIdsRespuestas(pregunta);
    }

    public void eliminarRespuesta(int positionPregunta, int positionRespuesta) {
        Pregunta pregunta = getListaPreguntas().get(positionPregunta);
        pregunta.getPosiblesRespuestasCategoricas().remove(positionRespuesta);
        actualizarIdsRespuestas(pregunta);
    }

    public void crearEncuesta() {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference("");
        DatabaseReference encuestaRef = ref.child("encuestas");

        List<String> encuestadoresAsignados = new ArrayList<>();
        encuesta.setEncuestadoresAsignados(encuestadoresAsignados);


        ApiFutures.addCallback(encuestaRef.push().setValueAsync(encuesta), new ApiFutureCallback<Void>() {
            @Override
            public void onFailure(Throwable t) {
                postBus(new CreateEncuestaEvent("Error al crear la encuesta", t));
            }

            @Override
            public void onSuccess(Void result) {
                postBus(new CreateEncuestaEvent(encuesta.getId()));
            }
        }, directExecutor());
    }

    public void editarEncuesta() {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference("");
        DatabaseReference encuestaRef = ref
                .child("encuestas")
                .child(encuesta.getId());

        ApiFutures.addCallback(encuestaRef.setValueAsync(encuesta), new ApiFutureCallback<Void>() {
            @Override
            public void onFailure(Throwable t) {
                postBus(new EditarEncuestaEvent("Error al crear la encuesta", t));
            }

            @Override
            public void onSuccess(Void result) {
                postBus(new EditarEncuestaEvent(encuesta.getId()));
            }
        }, directExecutor());
    }

    public void subirPregunta(int oldPosition) {
        Collections.swap(getListaPreguntas(), oldPosition, oldPosition - 1);
    }

    public void bajarPregunta(int oldPosition) {
        Collections.swap(getListaPreguntas(), oldPosition, oldPosition + 1);
    }

    public void clonarPregunta(int position, String nuevaDescripcion) {
        Pregunta preguntaOriginal = getListaPreguntas().get(position);
        Pregunta preguntaClonada = new Pregunta();

        preguntaClonada.setDescripcion(nuevaDescripcion);
        preguntaClonada.setTipo(preguntaOriginal.getTipo());
        preguntaClonada.setMin(preguntaOriginal.getMin());
        preguntaClonada.setMax(preguntaOriginal.getMax());

        if (preguntaOriginal.getPosiblesRespuestasCategoricas() != null) {
            for (Respuesta respuestaOriginal : preguntaOriginal.getPosiblesRespuestasCategoricas()) {
                Respuesta respuestaClonada = new Respuesta();
                respuestaClonada.setId(respuestaOriginal.getId());
                respuestaClonada.setDescripcion(respuestaOriginal.getDescripcion());
                preguntaClonada.addPosibleRespuestaCategorica(respuestaClonada);
            }
        }

        getListaPreguntas().add(position + 1, preguntaClonada);
    }

    public void subirRespuesta(int positionPregunta, int oldPosition) {
        Pregunta pregunta = getListaPreguntas().get(positionPregunta);
        List<Respuesta> respuestas = pregunta.getPosiblesRespuestasCategoricas();
        int newPosition = oldPosition - 1;

        if (MIXTA.equals(pregunta.getTipo())) {
            if (respuestas.get(oldPosition).getDescripcion().equals(RESPUESTA_MIXTA_DEFAULT) ||
                respuestas.get(newPosition).getDescripcion().equals(RESPUESTA_MIXTA_DEFAULT)) {
                return; // Do not allow swapping with the default "Otro..." answer
            }
        }
        
        Collections.swap(respuestas, oldPosition, newPosition);
        actualizarIdsRespuestas(pregunta);
    }

    public void bajarRespuesta(int positionPregunta, int oldPosition) {
        Pregunta pregunta = getListaPreguntas().get(positionPregunta);
        List<Respuesta> respuestas = pregunta.getPosiblesRespuestasCategoricas();
        int newPosition = oldPosition + 1;

        if (MIXTA.equals(pregunta.getTipo())) {
            if (respuestas.get(oldPosition).getDescripcion().equals(RESPUESTA_MIXTA_DEFAULT) ||
                    (newPosition < respuestas.size() && respuestas.get(newPosition).getDescripcion().equals(RESPUESTA_MIXTA_DEFAULT))) {
                return; // Do not allow swapping with the default "Otro..." answer
            }
        }

        Collections.swap(respuestas, oldPosition, newPosition);
        actualizarIdsRespuestas(pregunta);
    }

    private void actualizarIdsRespuestas(Pregunta pregunta) {
        for (int i = 0; i < pregunta.getPosiblesRespuestasCategoricas().size(); i++) {
            pregunta.getPosiblesRespuestasCategoricas().get(i).setId(i);
        }
    }

    public void setBackUpRespuestas(List<Respuesta> posiblesRespuestasCategoricas) {
        backUp = posiblesRespuestasCategoricas;
    }

    public List<Respuesta> getBackUpRespuestas() {
        if (!backUp.equals(null) && !backUp.isEmpty()) {
            return backUp;
        }
        return null;
    }

    public String getTitulo() {
        return encuesta.getTitulo();
    }

    public String getDescripcion() {
        return encuesta.getDescripcion();
    }

    public void clearEncuestadoresAsignados() {
        encuesta.setEncuestadoresAsignados(null);
    }

    public void clearId() {
        encuesta.setId(null);
    }
}
