package ar.com.oikosanalistas.admin.ui.view;

import ar.com.oikosanalistas.admin.ui.model.UserListModel;
import ar.com.oikosanalistas.admin.ui.model.pojo.Usuario;
import ar.com.oikosanalistas.admin.ui.view.renderer.ZebraStripeCellRenderer;
import com.google.firebase.auth.UserInfo;

import javax.swing.*;
import java.awt.*;
import java.util.List;

public class UsuariosFrame extends BaseFrame {

    private static final int WIDTH = 500;
    private static final int HEIGHT = 500;

    private JButton btnGuardarUsuario;
    private JTextField txtDisplayName;
    private JTextField txtEmail;
    private JTextField txtPassword;
    private JTextField txtUid;
    private JLabel lblUid;
    private JLabel lblNombre;
    private JLabel lblEmail;
    private JLabel lblPassword;
    private JList listUsuarios;
    private JPanel usuariosPanel;
    private JButton btnEliminarUsuario;
    private JButton btnLimpiarSeleccion;
    private UserListModel userListModel;

    public UsuariosFrame() {
        setSize(WIDTH, HEIGHT);
        setupUI();
        setContentPane(usuariosPanel);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(WindowConstants.DISPOSE_ON_CLOSE);
        setTitle("Gestión de Usuarios");
    }

    private void setupUI() {
        usuariosPanel = new JPanel(new GridBagLayout());
        usuariosPanel.setBorder(BorderFactory.createEmptyBorder(16, 16, 16, 16));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(4, 4, 4, 4);
        gbc.fill = GridBagConstraints.HORIZONTAL;

        // Form panel
        JPanel formPanel = new JPanel(new GridBagLayout());
        GridBagConstraints formGbc = new GridBagConstraints();
        formGbc.insets = new Insets(4, 4, 4, 4);
        formGbc.fill = GridBagConstraints.HORIZONTAL;
        
        // User ID
        lblUid = new JLabel("User ID:");
        formGbc.gridx = 0;
        formGbc.gridy = 0;
        formGbc.weightx = 0;
        formPanel.add(lblUid, formGbc);
        txtUid = new JTextField();
        txtUid.setEditable(false);
        formGbc.gridy = 1;
        formGbc.weightx = 1;
        formPanel.add(txtUid, formGbc);

        // Name
        lblNombre = new JLabel("Nombre:");
        formGbc.gridy = 2;
        formGbc.weightx = 0;
        formPanel.add(lblNombre, formGbc);
        txtDisplayName = new JTextField();
        formGbc.gridy = 3;
        formGbc.weightx = 1;
        formPanel.add(txtDisplayName, formGbc);

        // Email
        lblEmail = new JLabel("E-Mail:");
        formGbc.gridy = 4;
        formGbc.weightx = 0;
        formPanel.add(lblEmail, formGbc);
        txtEmail = new JTextField();
        formGbc.gridy = 5;
        formGbc.weightx = 1;
        formPanel.add(txtEmail, formGbc);

        // Password
        lblPassword = new JLabel("Contraseña:");
        formGbc.gridy = 6;
        formGbc.weightx = 0;
        formPanel.add(lblPassword, formGbc);
        txtPassword = new JTextField();
        formGbc.gridy = 7;
        formGbc.weightx = 1;
        formPanel.add(txtPassword, formGbc);

        // Buttons
        btnGuardarUsuario = new JButton("Guardar");
        btnEliminarUsuario = new JButton("Eliminar");
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        buttonPanel.add(btnGuardarUsuario);
        buttonPanel.add(btnEliminarUsuario);
        formGbc.gridy = 8;
        formGbc.gridwidth = 2;
        formPanel.add(buttonPanel, formGbc);
        
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.weightx = 0;
        gbc.weighty = 1;
        gbc.fill = GridBagConstraints.VERTICAL;
        gbc.anchor = GridBagConstraints.NORTH;
        usuariosPanel.add(formPanel, gbc);

        // User list
        listUsuarios = new JList<>();
        listUsuarios.setFont(new Font(Font.DIALOG, Font.PLAIN, 16));
        listUsuarios.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        listUsuarios.setCellRenderer(new ZebraStripeCellRenderer());
        JScrollPane scrollPane = new JScrollPane(listUsuarios);
        gbc.gridx = 1;
        gbc.gridy = 0;
        gbc.weightx = 1;
        gbc.weighty = 1;
        gbc.fill = GridBagConstraints.BOTH;
        usuariosPanel.add(scrollPane, gbc);
        
        // Clear selection button
        btnLimpiarSeleccion = new JButton("Limpiar selección");
        gbc.gridx = 1;
        gbc.gridy = 1;
        gbc.weightx = 0;
        gbc.weighty = 0;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.EAST;
        usuariosPanel.add(btnLimpiarSeleccion, gbc);
    }

    @Override
    JPanel getPanel() {
        return usuariosPanel;
    }

    public JButton getBtnGuardarUsuario() {
        return btnGuardarUsuario;
    }

    public JButton getBtnEliminarUsuario() { return btnEliminarUsuario; }

    public JList getListUsuarios() {
        return listUsuarios;
    }

    public UserListModel getUserListModel() {
        return userListModel;
    }

    public void setTxtDisplayName(String txtDisplayName) {
        this.txtDisplayName.setText(txtDisplayName);
    }

    public void setTxtEmail(String txtEmail) {
        this.txtEmail.setText(txtEmail);
    }

    public void setTxtPassword(String txtPassword) {
        this.txtPassword.setText(txtPassword);
    }

    public void setTxtUid(String txtUid) {
        this.txtUid.setText(txtUid);
    }

    public String getTxtDisplayName() {
        return txtDisplayName.getText();
    }

    public String getTxtEmail() {
        return txtEmail.getText();
    }

    public String getTxtPassword() {
        return txtPassword.getText();
    }

    public JButton getBtnLimpiarSeleccion() {
        return btnLimpiarSeleccion;
    }

    public void clearAll(){
        txtDisplayName.setText(null);
        txtEmail.setText(null);
        txtPassword.setText(null);
        txtUid.setText(null);
    }

    public void actualizarListaUsuarios(List<UserInfo> userList) {
        userListModel = new UserListModel();
        for (UserInfo user : userList) {
            userListModel.addUser(user);
        }
        listUsuarios.setModel(userListModel);
    }

    private void $$$setupUI$$$() {
        // Now managed by setupUI with GridBagLayout
    }

    public JComponent $$$getRootComponent$$$() {
        return usuariosPanel;
    }
}
