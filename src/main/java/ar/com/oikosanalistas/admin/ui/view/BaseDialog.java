package ar.com.oikosanalistas.admin.ui.view;

import com.google.errorprone.annotations.ForOverride;

import javax.swing.*;
import java.awt.event.KeyEvent;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;

public abstract class BaseDialog<L extends BaseDialog.PreguntaDialogListener, M extends BaseDialog.PreguntaDialogViewModel> extends JDialog {

    private static final int WIDTH = 400;
    private static final int HEIGHT = 500;

    private JButton buttonOK;
    private JButton buttonCancel;

    protected L listener;
    protected M viewModel;

    public interface PreguntaDialogListener {
    }

    public interface PreguntaDialogViewModel {
    }

    public BaseDialog() {
    }

    protected abstract void initViewModel();

    protected void initDefaultListeners() {
        buttonOK.addActionListener(e -> onOkButtonPressed());
        buttonCancel.addActionListener(e -> onCancelButtonPressed());
    }

    protected abstract void initExtraListeners();

    protected abstract void onOkButtonPressed();

    protected abstract void onCancelButtonPressed();

    @ForOverride
    protected void setupExtraComponents() {
    }

    protected void setupComponents(String titulo, L listener, JPanel contentPane, JButton buttonOK, JButton buttonCancel) {
        if (contentPane == null) {
            throw new IllegalArgumentException("contentPane cannot be null");
        }
        this.listener = listener;
        this.buttonOK = buttonOK;
        this.buttonCancel = buttonCancel;

        initViewModel();

        setSize(WIDTH, HEIGHT);
        setContentPane(contentPane);
        setModal(true);
        getRootPane().setDefaultButton(buttonOK);

        initDefaultListeners();
        initExtraListeners();

        setDefaultCloseOperation(DISPOSE_ON_CLOSE);
        addWindowListener(new WindowAdapter() {
            public void windowClosing(WindowEvent e) {
                onCancelButtonPressed();
            }
        });

        contentPane.registerKeyboardAction(e -> onCancelButtonPressed(), KeyStroke.getKeyStroke(KeyEvent.VK_ESCAPE, 0), JComponent.WHEN_ANCESTOR_OF_FOCUSED_COMPONENT);

        setTitle(titulo);
        setLocationRelativeTo(null);
    }
}
