package ar.com.oikosanalistas.admin.ui.model.pojo.pregunta;

import ar.com.oikosanalistas.admin.ui.model.pojo.respuesta.Respuesta;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import static ar.com.oikosanalistas.admin.utils.Constants.NUMERICA;

/**
 * Created by User on 26/02/2018.
 */
public final class Pregunta implements Serializable {

    private Integer id;
    private String descripcion;
    private String tipo;
    private Integer min;
    private Integer max;
    private List<Respuesta> posiblesRespuestasCategoricas;

    public Pregunta() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    public Integer getMin() {
        return min;
    }

    public void setMin(Integer min) {
        this.min = min;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public List<Respuesta> getPosiblesRespuestasCategoricas() {
        if (NUMERICA.equals(tipo)) {
            Respuesta respuesta = new Respuesta();
            respuesta.setId(0);
            respuesta.setDescripcion("Min: " + min + " - Max: " + max);
            List<Respuesta> respuestas = new ArrayList<>();
            respuestas.add(respuesta);
            return respuestas;
        } else {
            return posiblesRespuestasCategoricas;
        }
    }

    /**
     * Agrega una posible respuesta a la lista de respuestas posibles
     * @param respuestaCategorica nueva posible respuesta a agregar
     */
    public void addPosibleRespuestaCategorica(Respuesta respuestaCategorica) {
        if (posiblesRespuestasCategoricas == null || posiblesRespuestasCategoricas.isEmpty()) {
            posiblesRespuestasCategoricas = new ArrayList<>();
        }

        posiblesRespuestasCategoricas.add(respuestaCategorica);
    }

    public void clearPosiblesRespuestasCategoricas() {
        posiblesRespuestasCategoricas = new ArrayList<>();
    }

    public int getCantidadPosiblesRespuestasCategoricas() {
        if (posiblesRespuestasCategoricas == null || posiblesRespuestasCategoricas.isEmpty()) {
            return 0;
        }
        return posiblesRespuestasCategoricas.size();
    }
}
