package ar.com.oikosanalistas.admin.ui.controller;

import ar.com.oikosanalistas.admin.events.*;
import ar.com.oikosanalistas.admin.file.EncuestaFileCreator;
import ar.com.oikosanalistas.admin.file.MapFileCreator;
import ar.com.oikosanalistas.admin.ui.model.EncuestasListModel;
import ar.com.oikosanalistas.admin.ui.model.GenerarResultadosModel;
import ar.com.oikosanalistas.admin.ui.model.pojo.EncuestaRealizada;
import ar.com.oikosanalistas.admin.ui.view.GenerarResultadosFrame;
import org.greenrobot.eventbus.Subscribe;

import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.util.List;

import static ar.com.oikosanalistas.admin.file.FileCreator.promptForFolder;
import static ar.com.oikosanalistas.admin.utils.StringUtils.isEmpty;
import static ar.com.oikosanalistas.admin.utils.Constants.*;

public class GenerarResultadosController extends BaseController {

    private GenerarResultadosFrame resultadosFrame;
    private GenerarResultadosModel resultadosModel;
    private JList listaEncuestas;
    private JButton btnGenerar;
    private String idEncuesta;
    private List<EncuestaRealizada> encuestasRealizadas;


    public GenerarResultadosController() {
        resultadosModel = new GenerarResultadosModel();

        initComponents();
        initListeners();
    }

    private void initComponents() {
        resultadosFrame = new GenerarResultadosFrame();
        listaEncuestas = resultadosFrame.getListEncuestas();
        btnGenerar = resultadosFrame.getBtnGenerar();
    }

    private void initListeners() {
        btnGenerar.addActionListener(e -> {
            int position = listaEncuestas.getSelectedIndex();
            EncuestasListModel encuestasList = resultadosFrame.getEncuestasListModel();
            if (encuestasList != null) {
                if (position >= 0) {
                    resultadosFrame.showLoadingDialog();
                    idEncuesta = encuestasList.getEncuestaAt(position).getId();
                    resultadosModel.listAllUsersGenerar();
                }
            }
        });
    }

    public void showResultadosFrameWindow() {
        listAllEncuestasGenerar();
        resultadosFrame.setVisible(true);
    }

    private void listAllEncuestasGenerar() {
        resultadosFrame.showLoadingDialog();
        resultadosModel.listAllEncuestasGenerar();
    }

    @Subscribe
    public void onListAllEncuestasGenerarEvent(ListAllEncuestasGenerarEvent event) {
        if (event.isSuccess()) {
            resultadosFrame.showLoadingDialog();
            resultadosFrame.actualizarListaEncuestas(event.getResult());
        } else {
            System.out.println(event.getErrorMessage());
        }
        resultadosFrame.hideLoadingDialog();
    }

    @Subscribe
    public void onListAllUsersGenerarEvent(ListAllUsersGenerarEvent event) {
        if (event.isSuccess()) {
            resultadosModel.listAllEncuestasRealizadas(idEncuesta, event.getResult());
        } else {
            System.out.println(event.getErrorMessage());
            resultadosFrame.hideLoadingDialog();
        }
    }

    @Subscribe
    public void onListAllEncuestasRealizadasEvent(ListAllEncuestasRealizadasEvent event) {
        if (event.isSuccess()) {
            encuestasRealizadas = event.getResult();

            if (encuestasRealizadas != null) {
                resultadosModel.retrieveDynamicOperations();
            } else {
                resultadosFrame.hideLoadingDialog();
                resultadosFrame.showMessageDialog(NO_ENCUESTAS_REALIZADAS_MESSAGE);
            }
        } else {
            resultadosFrame.hideLoadingDialog();
            System.out.println(event.getErrorMessage());
            resultadosFrame.showMessageDialog(event.getErrorMessage());
        }
    }

    private String sanitizeFileName(String name) {
        return name.replaceAll("[^a-zA-Z0-9.-]", "_");
    }

    @Subscribe
    public void onDynamicOperationsEvent(DynamicOperationsEvent event) {
        if (event.isSuccess()) {
            resultadosModel.getEncuestaSegunId(idEncuesta);
        } else {
            resultadosFrame.hideLoadingDialog();
            System.out.println(event.getErrorMessage());
            resultadosFrame.showMessageDialog(event.getErrorMessage());
        }
    }

    @Subscribe
    public void onGetEncuestaSegunIdEvent(GetEncuestaSegunIdEvent event) {
        resultadosFrame.hideLoadingDialog();
        if (event.isSuccess()) {
            String nombreEncuesta = sanitizeFileName(event.getResult().getTitulo());
            String mapFileName = nombreEncuesta + "_mapa.html";
            String excelFileName = nombreEncuesta + "_resultados.xlsx";

            // Custom Dialog
            JDialog dialog = new JDialog(resultadosFrame, "Confirmar Exportación", true);
            JPanel panel = new JPanel(new BorderLayout(10, 10));
            panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

            // Info Panel
            JPanel infoPanel = new JPanel(new GridLayout(0, 1));
            infoPanel.setBorder(BorderFactory.createTitledBorder("Archivos a generar"));
            infoPanel.add(new JLabel(mapFileName));
            infoPanel.add(new JLabel(excelFileName));

            // Selection Panel
            JPanel selectionPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
            JButton selectDirButton = new JButton("Seleccionar carpeta...");
            JLabel selectedDirLabel = new JLabel("No se ha seleccionado ninguna carpeta.");
            selectionPanel.add(selectDirButton);
            selectionPanel.add(selectedDirLabel);

            // Bottom buttons
            JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
            JButton okButton = new JButton("OK");
            JButton cancelButton = new JButton("Cancel");
            okButton.setEnabled(false); // Disabled by default
            buttonsPanel.add(okButton);
            buttonsPanel.add(cancelButton);

            panel.add(infoPanel, BorderLayout.NORTH);
            panel.add(selectionPanel, BorderLayout.CENTER);
            panel.add(buttonsPanel, BorderLayout.SOUTH);

            final File[] selectedDir = new File[1];

            selectDirButton.addActionListener(e -> {
                JFileChooser fileChooser = new JFileChooser();
                fileChooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
                fileChooser.setDialogTitle("Seleccione la carpeta de destino");
                int result = fileChooser.showOpenDialog(resultadosFrame);
                if (result == JFileChooser.APPROVE_OPTION) {
                    selectedDir[0] = fileChooser.getSelectedFile();
                    selectedDirLabel.setText(selectedDir[0].getAbsolutePath());
                    okButton.setEnabled(true); // Enable OK button
                }
            });

            okButton.addActionListener(e -> {
                String mapPath = selectedDir[0].getAbsolutePath() + File.separator + mapFileName;
                String excelPath = selectedDir[0].getAbsolutePath() + File.separator + excelFileName;

                createMapsHTML(mapPath, resultadosModel.getLocationsList(encuestasRealizadas), resultadosModel.getHtmlPart1(), resultadosModel.getHtmlPart2());
                resultadosModel.setPreguntas(event.getResult().getPreguntasList());
                createExcel(excelPath, encuestasRealizadas, event.getResult().getTitulo());
                dialog.dispose();
            });

            cancelButton.addActionListener(e -> dialog.dispose());

            dialog.setContentPane(panel);
            dialog.pack();
            dialog.setLocationRelativeTo(resultadosFrame);
            dialog.setVisible(true);

        } else {
            System.out.println(event.getErrorMessage());
            resultadosFrame.showMessageDialog(event.getErrorMessage());
        }
    }

    private void createExcel(String filename, List<EncuestaRealizada> encuestasRealizadas, String tituloEncuesta) {
        EncuestaFileCreator encuestaFileCreator = new EncuestaFileCreator(filename, encuestasRealizadas, tituloEncuesta, resultadosModel);
        encuestaFileCreator.createFile(resultadosFrame);
    }

    private void createMapsHTML(String filename, String locationsList, String htmlPart1, String htmlPart2) {
        MapFileCreator mapFileCreator = new MapFileCreator(filename, locationsList, htmlPart1, htmlPart2);
        mapFileCreator.createFile(resultadosFrame);
    }
}
