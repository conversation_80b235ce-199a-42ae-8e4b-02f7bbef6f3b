package ar.com.oikosanalistas.admin.ui.model;

import ar.com.oikosanalistas.admin.events.CheckVersionEvent;
import ar.com.oikosanalistas.admin.ui.model.pojo.LastVersion;
import com.google.firebase.database.*;

public class MainFrameModel extends BaseModel {

    public void checkVersion(){
        System.out.println("MainFrameModel: Iniciando consulta a Firebase...");
        try {
            final FirebaseDatabase database = FirebaseDatabase.getInstance();
            DatabaseReference ref = database.getReference("last_versions");
            ref.addListenerForSingleValueEvent(new ValueEventListener() {
                @Override
                public void onDataChange(DataSnapshot snapshot) {
                    System.out.println("MainFrameModel: Datos recibidos de Firebase");
                    LastVersion version = snapshot.getValue(LastVersion.class);
                    System.out.println("MainFrameModel: Versión obtenida: " + (version != null ? version.getAdmin() : "null"));
                    System.out.println("MainFrameModel: Enviando evento...");
                    postBus(new CheckVersionEvent(version));
                }
                @Override
                public void onCancelled(DatabaseError error) {
                    System.out.println("MainFrameModel: Error en Firebase: " + error.getMessage());
                    postBus(new CheckVersionEvent("No se pudo verificar la version.",error.toException()));
                }
            });
        } catch (Exception e) {
            System.out.println("MainFrameModel: Excepción al conectar con Firebase: " + e.getMessage());
            postBus(new CheckVersionEvent("Error de conexión.", e));
        }
    }
}
