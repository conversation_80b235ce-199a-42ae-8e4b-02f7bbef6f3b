package ar.com.oikosanalistas.admin.ui.view;

import com.github.lgooddatepicker.components.DatePicker;
import ar.com.oikosanalistas.admin.ui.model.pojo.pregunta.Pregunta;
import ar.com.oikosanalistas.admin.ui.model.pojo.respuesta.Respuesta;
import ar.com.oikosanalistas.admin.ui.view.renderer.ZebraStripeCellRenderer;
import ar.com.oikosanalistas.admin.utils.Constants;

import javax.swing.*;
import java.awt.*;

public class CrearEncuestasFrame extends BaseFrame {

    private static final int WIDTH = 800;
    private static final int HEIGHT = 600;

    private JPanel panelCrearEncuestas;
    private JTextField txtTitulo;
    private JList listaPreguntas;
    private JTextField txtDescripcion;
    private JList listaRespuestas;
    private JButton btnCrearEncuesta;
    private JButton btnDescartarEncuesta;
    private JButton btnAgregarPregunta;
    private JButton btnEliminarPregunta;
    private JButton btnEliminarRespuesta;
    private DatePicker datePickerDesde;
    private DatePicker datePickerHasta;
    private JButton btnEditarPregunta;
    private JButton btnSubirPregunta;
    private JButton btnBajarPregunta;
    private JButton btnSubirRespuesta;
    private JButton btnBajarRespuesta;
    private JButton btnCopiar;
    private JButton btnPegar;
    private JButton btnEditarRespuesta;
    private JButton btnAgregarRespuesta;
    private JButton btnClonar;

    public CrearEncuestasFrame() {
        setupUI();
        setSize(WIDTH, HEIGHT);
        setMinimumSize(new Dimension(700, 500));
        setContentPane(panelCrearEncuestas);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(WindowConstants.DISPOSE_ON_CLOSE);
        setTitle("Crear Nueva Encuesta");
    }

    private void setupUI() {
        panelCrearEncuestas = new JPanel(new BorderLayout(10, 10));
        panelCrearEncuestas.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        panelCrearEncuestas.add(createTopPanel(), BorderLayout.NORTH);
        panelCrearEncuestas.add(createCenterPanel(), BorderLayout.CENTER);
        panelCrearEncuestas.add(createBottomPanel(), BorderLayout.SOUTH);

        // Initial state for buttons
        setQuestionButtonsEnabled(false);
        setAnswerButtonsEnabled(false);
        btnAgregarRespuesta.setEnabled(false);

        // Listeners to enable/disable buttons based on selection
        listaPreguntas.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                boolean isSelected = listaPreguntas.getSelectedIndex() != -1;
                setQuestionButtonsEnabled(isSelected);

                boolean enableAddAnswer = false;
                if (isSelected) {
                    Pregunta pregunta = (Pregunta) listaPreguntas.getSelectedValue();
                    String tipo = pregunta.getTipo();
                    if (tipo.equals(Constants.CATEGORICA) || tipo.equals(Constants.MULTIPLE) || tipo.equals(Constants.MIXTA)) {
                        enableAddAnswer = true;
                    }
                }
                btnAgregarRespuesta.setEnabled(enableAddAnswer);
            }
        });
        listaRespuestas.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                setAnswerButtonsEnabled(listaRespuestas.getSelectedIndex() != -1);
            }
        });

        // --- Renderers ---
        listaPreguntas.setCellRenderer(new ZebraStripeCellRenderer());
        listaRespuestas.setCellRenderer(new ZebraStripeCellRenderer());
    }

    private JPanel createTopPanel() {
        JPanel topPanel = new JPanel(new GridBagLayout());
        topPanel.setBorder(BorderFactory.createTitledBorder("Detalles de la Encuesta"));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;

        // Title
        gbc.gridx = 0;
        gbc.gridy = 0;
        topPanel.add(new JLabel("Título:"), gbc);
        txtTitulo = new JTextField();
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        gbc.weightx = 1.0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        topPanel.add(txtTitulo, gbc);

        // Description
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.gridwidth = 1;
        gbc.weightx = 0;
        gbc.fill = GridBagConstraints.NONE;
        topPanel.add(new JLabel("Descripción:"), gbc);
        txtDescripcion = new JTextField();
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        gbc.weightx = 1.0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        topPanel.add(txtDescripcion, gbc);

        // Dates
        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.gridwidth = 1;
        gbc.weightx = 0;
        gbc.fill = GridBagConstraints.NONE;
        topPanel.add(new JLabel("Válida desde:"), gbc);

        datePickerDesde = new DatePicker();
        gbc.gridx = 1;
        topPanel.add(datePickerDesde, gbc);

        gbc.gridx = 2;
        topPanel.add(new JLabel("Válida hasta:"), gbc);

        datePickerHasta = new DatePicker();
        gbc.gridx = 3;
        topPanel.add(datePickerHasta, gbc);

        return topPanel;
    }

    private JSplitPane createCenterPanel() {
        // Questions Panel
        JPanel questionsPanel = new JPanel(new BorderLayout(5, 5));
        questionsPanel.setBorder(BorderFactory.createTitledBorder("Preguntas"));
        listaPreguntas = new JList();
        questionsPanel.add(new JScrollPane(listaPreguntas), BorderLayout.CENTER);

        // --- Reorder buttons on the side ---
        JPanel questionReorderButtons = new JPanel();
        questionReorderButtons.setLayout(new BoxLayout(questionReorderButtons, BoxLayout.Y_AXIS));
        btnSubirPregunta = new JButton("↑");
        btnBajarPregunta = new JButton("↓");
        questionReorderButtons.add(Box.createVerticalGlue());
        questionReorderButtons.add(btnSubirPregunta);
        questionReorderButtons.add(Box.createRigidArea(new Dimension(0, 5)));
        questionReorderButtons.add(btnBajarPregunta);
        questionReorderButtons.add(Box.createVerticalGlue());
        questionsPanel.add(questionReorderButtons, BorderLayout.EAST);

        JPanel questionButtons = new JPanel(new FlowLayout(FlowLayout.CENTER));
        btnAgregarPregunta = new JButton("Agregar");
        btnEditarPregunta = new JButton("Editar");
        btnEliminarPregunta = new JButton("Eliminar");
        btnClonar = new JButton("Clonar");
        questionButtons.add(btnAgregarPregunta);
        questionButtons.add(btnEditarPregunta);
        questionButtons.add(btnEliminarPregunta);
        questionButtons.add(btnClonar);
        questionsPanel.add(questionButtons, BorderLayout.SOUTH);

        // Answers Panel
        JPanel answersPanel = new JPanel(new BorderLayout(5, 5));
        answersPanel.setBorder(BorderFactory.createTitledBorder("Respuestas"));
        listaRespuestas = new JList();
        answersPanel.add(new JScrollPane(listaRespuestas), BorderLayout.CENTER);

        // --- Action buttons at the bottom ---
        JPanel answerActionButtons = new JPanel(new FlowLayout(FlowLayout.CENTER));
        btnEditarRespuesta = new JButton("Editar");
        btnEliminarRespuesta = new JButton("Eliminar");
        btnAgregarRespuesta = new JButton("Agregar");
        answerActionButtons.add(btnEditarRespuesta);
        answerActionButtons.add(btnEliminarRespuesta);
        answerActionButtons.add(btnAgregarRespuesta);
        answersPanel.add(answerActionButtons, BorderLayout.SOUTH);

        // --- Reorder buttons on the side ---
        JPanel answerReorderButtons = new JPanel();
        answerReorderButtons.setLayout(new BoxLayout(answerReorderButtons, BoxLayout.Y_AXIS));
        btnSubirRespuesta = new JButton("↑");
        btnBajarRespuesta = new JButton("↓");
        
        // Add some spacing and alignment for the reorder buttons
        answerReorderButtons.add(Box.createVerticalGlue());
        answerReorderButtons.add(btnSubirRespuesta);
        answerReorderButtons.add(Box.createRigidArea(new Dimension(0, 5)));
        answerReorderButtons.add(btnBajarRespuesta);
        answerReorderButtons.add(Box.createVerticalGlue());
        
        answersPanel.add(answerReorderButtons, BorderLayout.EAST);
        
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, questionsPanel, answersPanel);
        splitPane.setResizeWeight(0.5);
        return splitPane;
    }

    private JPanel createBottomPanel() {
        JPanel bottomPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        btnCrearEncuesta = new JButton("Crear Encuesta");
        btnDescartarEncuesta = new JButton("Descartar Encuesta");
        bottomPanel.add(btnCrearEncuesta);
        bottomPanel.add(btnDescartarEncuesta);
        return bottomPanel;
    }
    
    private void setQuestionButtonsEnabled(boolean enabled) {
        btnEditarPregunta.setEnabled(enabled);
        btnEliminarPregunta.setEnabled(enabled);
        btnSubirPregunta.setEnabled(enabled);
        btnBajarPregunta.setEnabled(enabled);
        btnClonar.setEnabled(enabled);
    }

    private void setAnswerButtonsEnabled(boolean enabled) {
        btnEditarRespuesta.setEnabled(enabled);
        btnEliminarRespuesta.setEnabled(enabled);
        btnSubirRespuesta.setEnabled(enabled);
        btnBajarRespuesta.setEnabled(enabled);
    }

    public void setModoEdicion(String nombreEncuesta) {
        setTitle("Editando: " + nombreEncuesta);
        btnCrearEncuesta.setText("Guardar Cambios");
    }

    @Override
    JPanel getPanel() {
        return panelCrearEncuestas;
    }

    public JPanel getPanelCrearEncuestas() {
        return panelCrearEncuestas;
    }

    public JTextField getTxtTitulo() {
        return txtTitulo;
    }

    public JList getListaPreguntas() {
        return listaPreguntas;
    }

    public JTextField getTxtDescripcion() {
        return txtDescripcion;
    }

    public DatePicker getDatePickerDesde() {
        return datePickerDesde;
    }

    public DatePicker getDatePickerHasta() {
        return datePickerHasta;
    }

    public JButton getBtnCrearEncuesta() {
        return btnCrearEncuesta;
    }

    public JButton getBtnDescartarEncuesta() {
        return btnDescartarEncuesta;
    }

    public JButton getBtnAgregarPregunta() {
        return btnAgregarPregunta;
    }

    public JButton getBtnEliminarPregunta() {
        return btnEliminarPregunta;
    }

    public JButton getBtnEditarPregunta() {
        return btnEditarPregunta;
    }

    public JButton getBtnSubirPregunta() {
        return btnSubirPregunta;
    }

    public JButton getBtnBajarPregunta() {
        return btnBajarPregunta;
    }

    public JButton getBtnSubirRespuesta() {
        return btnSubirRespuesta;
    }

    public JButton getBtnBajarRespuesta() {
        return btnBajarRespuesta;
    }

    public JButton getBtnCopiar() {        return btnCopiar;    }

    public JButton getBtnPegar() {        return btnPegar;    }

    public JButton getBtnEditarRespuesta() {        return btnEditarRespuesta;    }

    public JButton getBtnEliminarRespuesta() {
        return btnEliminarRespuesta;
    }

    public JButton getBtnAgregarRespuesta() {
        return btnAgregarRespuesta;
    }

    public JButton getBtnClonar() {
        return btnClonar;
    }

    public JList getListaRespuestas() {
        return listaRespuestas;
    }

    public void clearAll(){
        txtDescripcion.setText(null);
        txtTitulo.setText(null);
        datePickerDesde.clear();
        datePickerHasta.clear();
        listaPreguntas.setListData(new Object[0]);
        listaRespuestas.setListData(new Object[0]);
    }

    /**
     * @noinspection ALL
     */
    public JComponent $$$getRootComponent$$$() {
        return panelCrearEncuestas;
    }
}
