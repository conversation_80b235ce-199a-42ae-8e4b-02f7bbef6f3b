package ar.com.oikosanalistas.admin.ui.view;

import ar.com.oikosanalistas.admin.ui.model.EncuestasListModel;
import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import ar.com.oikosanalistas.admin.ui.view.renderer.ZebraStripeCellRenderer;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.util.List;

public class SeleccionarEncuestasFrame extends BaseFrame {

    private JList<Encuesta> listaEncuestas;
    private JPanel mainPanel;
    private JButton seleccionarEncuestaBtn;
    private JButton cancelarEncuestaBtn;
    private EncuestasListModel encuestasListModel;

    public SeleccionarEncuestasFrame() {
        setTitle("Seleccionar Encuesta para Editar");
        setSize(400, 600);
        setDefaultCloseOperation(WindowConstants.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);

        mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(new EmptyBorder(10, 10, 10, 10));
        setContentPane(mainPanel);

        // --- Lista de Encuestas ---
        listaEncuestas = new JList<>();
        JScrollPane scrollPane = new JScrollPane(listaEncuestas);
        scrollPane.setBorder(new TitledBorder("Encuestas disponibles"));
        mainPanel.add(scrollPane, BorderLayout.CENTER);

        // --- Panel de botones ---
        seleccionarEncuestaBtn = new JButton("Seleccionar");
        cancelarEncuestaBtn = new JButton("Cancelar");
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 0));
        buttonPanel.add(seleccionarEncuestaBtn);
        buttonPanel.add(cancelarEncuestaBtn);
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        listaEncuestas.setCellRenderer(new ZebraStripeCellRenderer());
    }

    @Override
    JPanel getPanel() {
        return mainPanel;
    }

    public JButton getCancelarEncuestaBtn() {
        return cancelarEncuestaBtn;
    }

    public JButton getSeleccionarEncuestaBtn() {
        return seleccionarEncuestaBtn;
    }

    public JList<Encuesta> getListaEncuestas() {
        return listaEncuestas;
    }

    public JPanel getEncuestasPanel() {
        return mainPanel;
    }

    public void actualizarListaEncuestas(List<Encuesta> encuestasList) {
        encuestasListModel = new EncuestasListModel();
        for (Encuesta encuesta : encuestasList) {
            encuestasListModel.addEncuesta(encuesta);
        }
        listaEncuestas.setModel(encuestasListModel);
    }
}
