package ar.com.oikosanalistas.admin.ui.controller;

import ar.com.oikosanalistas.admin.events.CreateEncuestaEvent;
import ar.com.oikosanalistas.admin.ui.model.CrearEncuestasModel;
import ar.com.oikosanalistas.admin.ui.model.PreguntasListModel;
import ar.com.oikosanalistas.admin.ui.model.RespuestasListModel;
import ar.com.oikosanalistas.admin.ui.model.pojo.pregunta.Pregunta;
import ar.com.oikosanalistas.admin.ui.model.pojo.respuesta.Respuesta;
import ar.com.oikosanalistas.admin.ui.view.*;
import ar.com.oikosanalistas.admin.ui.view.viewmodel.*;
import ar.com.oikosanalistas.admin.utils.Constants;
import com.github.lgooddatepicker.components.DatePicker;
import org.greenrobot.eventbus.Subscribe;

import javax.swing.*;
import javax.swing.text.NumberFormatter;

import static ar.com.oikosanalistas.admin.utils.Constants.*;
import static ar.com.oikosanalistas.admin.utils.StringUtils.isEmpty;
import static ar.com.oikosanalistas.admin.utils.ValidatorUtils.curarString;
import java.text.NumberFormat;
import java.awt.GridBagLayout;
import java.awt.GridBagConstraints;
import java.awt.Insets;
import java.awt.FlowLayout;
import java.util.List;

public class CrearEncuestasController extends BaseController implements CrearPreguntaDialog.CrearPreguntaDialogListener {

    protected CrearEncuestasFrame crearEncuestasFrame;
    protected CrearEncuestasModel crearEncuestasModel;

    private JTextField txtTitulo;
    private JTextField txtDescripcion;
    private DatePicker datePickerDesde;
    private DatePicker datePickerHasta;
    private JButton btnDescartarEncuesta;
    private JButton btnAgregarPregunta;
    private JButton btnEditarPregunta;
    private JButton btnEditarRespuesta;
    private JButton btnEliminarPregunta;
    private JButton btnEliminarRespuesta;
    private JButton btnAgregarRespuesta;
    private JButton btnCrearEncuesta;
    private JButton btnSubirPregunta;
    private JButton btnBajarPregunta;
    private JButton btnSubirRespuesta;
    private JButton btnBajarRespuesta;
    private JButton btnClonar;
    private JList listaPreguntas;
    private JList listaRespuestas;

    public CrearEncuestasController() {
        crearEncuestasModel = new CrearEncuestasModel();

        initComponents();
        initListeners();
    }

    protected void initComponents() {
        crearEncuestasFrame = new CrearEncuestasFrame();
        txtTitulo = crearEncuestasFrame.getTxtTitulo();
        txtDescripcion = crearEncuestasFrame.getTxtDescripcion();
        datePickerDesde = crearEncuestasFrame.getDatePickerDesde();
        datePickerHasta = crearEncuestasFrame.getDatePickerHasta();
        btnDescartarEncuesta = crearEncuestasFrame.getBtnDescartarEncuesta();
        btnAgregarPregunta = crearEncuestasFrame.getBtnAgregarPregunta();
        btnEditarPregunta = crearEncuestasFrame.getBtnEditarPregunta();
        btnEditarRespuesta = crearEncuestasFrame.getBtnEditarRespuesta();
        btnEliminarPregunta = crearEncuestasFrame.getBtnEliminarPregunta();
        btnEliminarRespuesta = crearEncuestasFrame.getBtnEliminarRespuesta();
        btnAgregarRespuesta = crearEncuestasFrame.getBtnAgregarRespuesta();
        btnCrearEncuesta = crearEncuestasFrame.getBtnCrearEncuesta();
        btnSubirPregunta = crearEncuestasFrame.getBtnSubirPregunta();
        btnBajarPregunta = crearEncuestasFrame.getBtnBajarPregunta();
        btnSubirRespuesta = crearEncuestasFrame.getBtnSubirRespuesta();
        btnBajarRespuesta = crearEncuestasFrame.getBtnBajarRespuesta();
        btnClonar = crearEncuestasFrame.getBtnClonar();
        listaPreguntas = crearEncuestasFrame.getListaPreguntas();
        listaRespuestas = crearEncuestasFrame.getListaRespuestas();
    }

    protected void initListeners() {
        listaPreguntas.addListSelectionListener(e -> actualizarListaRespuestas());
        listaRespuestas.addListSelectionListener(e -> actualizarEstadoBotonesRespuesta());

        btnAgregarPregunta.addActionListener(e -> {
            CrearPreguntaDialog dialog = new CrearPreguntaDialog("Crear Nueva Pregunta", this);
            dialog.setVisible(true);
        });

        btnEditarPregunta.addActionListener(e -> {
            int position = listaPreguntas.getSelectedIndex();
            if (position >= 0) {
                Pregunta pregunta = crearEncuestasModel.getListaPreguntas().get(position);
                String nuevaDescripcion = mostrarDialogInput("Editar pregunta", "Actual: " + pregunta.getDescripcion(), pregunta.getDescripcion());
                if (!isEmpty(nuevaDescripcion)) {
                    crearEncuestasModel.actualizarDescripcionPregunta(position, nuevaDescripcion);
                    actualizarListaPreguntasYRespuestas();
                    listaPreguntas.setSelectedIndex(position);
                }
            }
        });

        btnEditarRespuesta.addActionListener(e -> {
            int positionRespuesta = listaRespuestas.getSelectedIndex();
            int positionPregunta = listaPreguntas.getSelectedIndex();

            if (positionRespuesta >= 0 && positionPregunta >= 0) {
                Pregunta pregunta = crearEncuestasModel.getListaPreguntas().get(positionPregunta);

                if (pregunta.getTipo().equals(NUMERICA)) {
                    // --- Custom Dialog for Numeric Question Range ---
                    JPanel panel = new JPanel(new GridBagLayout());
                    GridBagConstraints gbc = new GridBagConstraints();
                    gbc.insets = new Insets(5, 5, 5, 5);

                    NumberFormatter intFormatter = new NumberFormatter(NumberFormat.getIntegerInstance());
                    intFormatter.setValueClass(Integer.class);
                    intFormatter.setAllowsInvalid(false);
                    intFormatter.setMinimum(0);

                    JFormattedTextField txtMin = new JFormattedTextField(intFormatter);
                    JFormattedTextField txtMax = new JFormattedTextField(intFormatter);
                    txtMin.setValue(pregunta.getMin());
                    txtMax.setValue(pregunta.getMax());
                    txtMin.setColumns(5);
                    txtMax.setColumns(5);

                    gbc.gridx = 0;
                    gbc.gridy = 0;
                    panel.add(new JLabel("Valor Mínimo:"), gbc);
                    gbc.gridx = 1;
                    panel.add(txtMin, gbc);

                    gbc.gridx = 0;
                    gbc.gridy = 1;
                    panel.add(new JLabel("Valor Máximo:"), gbc);
                    gbc.gridx = 1;
                    panel.add(txtMax, gbc);

                    int result = JOptionPane.showConfirmDialog(crearEncuestasFrame, panel, "Editar Rango Numérico", JOptionPane.OK_CANCEL_OPTION, JOptionPane.PLAIN_MESSAGE);

                    if (result == JOptionPane.OK_OPTION) {
                        int newMin = (int) txtMin.getValue();
                        int newMax = (int) txtMax.getValue();

                        if (newMin >= newMax) {
                            crearEncuestasFrame.showMessageDialog("El valor mínimo no puede ser mayor o igual al valor máximo.");
                            return;
                        }

                        crearEncuestasModel.actualizarValoresPreguntaNumerica(positionPregunta, newMin, newMax);
                        actualizarListaPreguntasYRespuestas();
                        listaPreguntas.setSelectedIndex(positionPregunta);
                        listaRespuestas.setSelectedIndex(positionRespuesta);
                    }

                } else {
                    // --- Existing logic for other question types ---
                    if (pregunta.getTipo().equals(MIXTA) && pregunta.getPosiblesRespuestasCategoricas().get(positionRespuesta).getDescripcion().equals(RESPUESTA_MIXTA_DEFAULT)) {
                        crearEncuestasFrame.showMessageDialog("No se puede editar la respuesta por defecto");
                        return;
                    }

                    String nuevaDescripcion = mostrarDialogInput("Editar Respuesta", "Actual: " + pregunta.getPosiblesRespuestasCategoricas().get(positionRespuesta).getDescripcion(), pregunta.getPosiblesRespuestasCategoricas().get(positionRespuesta).getDescripcion());
                    if (!isEmpty(nuevaDescripcion)) {
                        crearEncuestasModel.actualizarDescripcionRespuesta(positionPregunta, positionRespuesta, nuevaDescripcion);
                        actualizarListaPreguntasYRespuestas();

                        listaPreguntas.setSelectedIndex(positionPregunta);
                        listaRespuestas.setSelectedIndex(positionRespuesta);
                    }
                }
            }
        });

        btnEliminarPregunta.addActionListener(e -> {
            int position = listaPreguntas.getSelectedIndex();
            if (position >= 0) {
                Pregunta pregunta = crearEncuestasModel.getListaPreguntas().get(position);
                if (mostrarDialogConfirmacion("Eliminar pregunta", "Descripción: " + pregunta.getDescripcion())) {
                    crearEncuestasModel.eliminarPregunta(position);
                    actualizarListaPreguntasYRespuestas();
                }
            }
        });

        btnEliminarRespuesta.addActionListener(e -> {
            int positionPregunta = listaPreguntas.getSelectedIndex();
            int positionRespuesta = listaRespuestas.getSelectedIndex();
            if (positionPregunta >= 0 && positionRespuesta >= 0) {
                Pregunta pregunta = crearEncuestasModel.getListaPreguntas().get(positionPregunta);
                if (pregunta.getPosiblesRespuestasCategoricas().size() == 1) {
                    crearEncuestasFrame.showMessageDialog("No se puede eliminar la última respuesta de una pregunta.");
                    return;
                }
                if (mostrarDialogConfirmacion("Eliminar respuesta", "¿Estás seguro de que deseas eliminar esta respuesta?")) {
                    crearEncuestasModel.eliminarRespuesta(positionPregunta, positionRespuesta);
                    actualizarListaPreguntasYRespuestas();
                    listaPreguntas.setSelectedIndex(positionPregunta);
                }
            }
        });

        btnAgregarRespuesta.addActionListener(e -> {
            int positionPregunta = listaPreguntas.getSelectedIndex();
            if (positionPregunta >= 0) {
                String nuevaRespuesta = mostrarDialogInput("Agregar Respuesta", "Nueva respuesta:");
                if (!isEmpty(nuevaRespuesta)) {
                    crearEncuestasModel.agregarRespuesta(positionPregunta, nuevaRespuesta);
                    actualizarListaPreguntasYRespuestas();
                    listaPreguntas.setSelectedIndex(positionPregunta);
                    listaRespuestas.setSelectedIndex(listaRespuestas.getModel().getSize() - 1);
                }
            }
        });

        btnDescartarEncuesta.addActionListener(e -> {
            if (mostrarDialogConfirmacion("Descartar Encuesta", "¿Estás seguro que deseas descartar los cambios? La ventana se cerrará.")) {
                crearEncuestasModel.resetEncuesta();
                crearEncuestasFrame.dispose();
            }
        });

        btnCrearEncuesta.addActionListener(e -> {
            String titulo = curarString(txtTitulo.getText());
            String descripcion = txtDescripcion.getText();
            String fechaInicio = datePickerDesde.getText();
            String fechaFin = datePickerHasta.getText();

            if (!isEmpty(titulo) && !isEmpty(descripcion)) {
                crearEncuestasModel.setTitulo(titulo);
                crearEncuestasModel.setDescripcion(descripcion);
                crearEncuestasModel.setFechaInicio(fechaInicio);
                crearEncuestasModel.setFechaFin(fechaFin);
                if (mostrarDialogConfirmacion("Crear encuesta", "¿Estás seguro de continuar?")) {
                    crearEncuestasFrame.showLoadingDialog();
                    crearEncuesta();
                    crearEncuestasModel.resetEncuesta();
                }
            }
        });

        btnSubirPregunta.addActionListener(e -> {
            int position = listaPreguntas.getSelectedIndex();
            if (position > 0) {
                crearEncuestasModel.subirPregunta(position);
                actualizarListaPreguntasYRespuestas();
                listaPreguntas.setSelectedIndex(position - 1);
            }
        });

        btnBajarPregunta.addActionListener(e -> {
            int position = listaPreguntas.getSelectedIndex();
            if (position < listaPreguntas.getModel().getSize() - 1) {
                crearEncuestasModel.bajarPregunta(position);
                actualizarListaPreguntasYRespuestas();
                listaPreguntas.setSelectedIndex(position + 1);
            }
        });

        btnSubirRespuesta.addActionListener(e -> {
            int positionPregunta = listaPreguntas.getSelectedIndex();
            int positionRespuesta = listaRespuestas.getSelectedIndex();
            if (positionPregunta >= 0 && positionRespuesta > 0) {
                crearEncuestasModel.subirRespuesta(positionPregunta, positionRespuesta);
                actualizarListaPreguntasYRespuestas();
                listaPreguntas.setSelectedIndex(positionPregunta);
                listaRespuestas.setSelectedIndex(positionRespuesta - 1);
            }
        });

        btnBajarRespuesta.addActionListener(e -> {
            int positionPregunta = listaPreguntas.getSelectedIndex();
            int positionRespuesta = listaRespuestas.getSelectedIndex();
            if (positionPregunta >= 0 && positionRespuesta < listaRespuestas.getModel().getSize() - 1) {
                crearEncuestasModel.bajarRespuesta(positionPregunta, positionRespuesta);
                actualizarListaPreguntasYRespuestas();
                listaPreguntas.setSelectedIndex(positionPregunta);
                listaRespuestas.setSelectedIndex(positionRespuesta + 1);
            }
        });

        btnClonar.addActionListener(e -> {
            int position = listaPreguntas.getSelectedIndex();
            if (position >= 0) {
                Pregunta preguntaOriginal = crearEncuestasModel.getListaPreguntas().get(position);
                String nuevaDescripcion = mostrarDialogInput("Clonar Pregunta", "Ingrese la descripción para la nueva pregunta:", preguntaOriginal.getDescripcion());
                if (!isEmpty(nuevaDescripcion)) {
                    crearEncuestasModel.clonarPregunta(position, nuevaDescripcion);
                    actualizarListaPreguntasYRespuestas();
                    listaPreguntas.setSelectedIndex(position + 1);
                }
            }
        });
    }

    protected void crearEncuesta() {
        crearEncuestasModel.crearEncuesta();
    }

    /**
     * Muestra un dialog de confirmación con un cierto titulo y mensaje.
     *
     * @param title   Titulo del dialog
     * @param message Mensaje del dialog
     * @return true si el usuario eligio opcion SI, false en caso contrario
     */
    private boolean mostrarDialogConfirmacion(String title, String message) {
        int reply = JOptionPane.showConfirmDialog(null, message, title, JOptionPane.YES_NO_OPTION);
        return reply == JOptionPane.YES_OPTION;
    }

    private String mostrarDialogInput(String titulo, String message) {
        return (String) JOptionPane.showInputDialog(null, message, titulo, JOptionPane.PLAIN_MESSAGE);
    }

    private String mostrarDialogInput(String titulo, String message, String prefilledText) {
        return (String) JOptionPane.showInputDialog(
                null,
                message,
                titulo,
                JOptionPane.PLAIN_MESSAGE,
                null,
                null,
                prefilledText
        );
    }

    public void showFrameWindow() {
        crearEncuestasFrame.setVisible(true);
    }

    protected void actualizarTitulo() {
        txtTitulo.setText(crearEncuestasModel.getTitulo());
    }

    protected void actualizarDescripcion() {
        txtDescripcion.setText(crearEncuestasModel.getDescripcion());
    }

    /**
     * Actualiza la lista de preguntas que se ven en la UI
     */
    public void actualizarListaPreguntasYRespuestas() {
        listaPreguntas.setModel(new PreguntasListModel(crearEncuestasModel.getListaPreguntas()));
        actualizarListaRespuestas();
    }

    /**
     * Actualiza la lista de respuestas que se ven en la UI
     */
    public void actualizarListaRespuestas() {
        int selectedIndex = listaPreguntas.getSelectedIndex();
        if (selectedIndex >= 0) {
            Pregunta pregunta = crearEncuestasModel.getListaPreguntas().get(selectedIndex);
            listaRespuestas.setModel(new RespuestasListModel(pregunta.getPosiblesRespuestasCategoricas()));
            actualizarEstadoBotonesRespuesta();
        } else {
            listaRespuestas.setModel(new DefaultListModel<>());
            actualizarEstadoBotonesRespuesta();
        }
    }

    private void actualizarEstadoBotonesRespuesta() {
        int selectedIndex = listaRespuestas.getSelectedIndex();
        boolean isSelected = selectedIndex >= 0;

        btnEditarRespuesta.setEnabled(isSelected);
        btnEliminarRespuesta.setEnabled(isSelected);

        int selectedPreguntaIndex = listaPreguntas.getSelectedIndex();
        if (selectedPreguntaIndex >= 0) {
            Pregunta pregunta = crearEncuestasModel.getListaPreguntas().get(selectedPreguntaIndex);
            if (pregunta.getTipo().equals(NUMERICA) || pregunta.getTipo().equals(TEXTO)) {
                btnAgregarRespuesta.setEnabled(false);
                btnEliminarRespuesta.setEnabled(false);
            } else {
                btnAgregarRespuesta.setEnabled(true);
            }

            if (pregunta.getTipo().equals(NUMERICA)){
                btnEditarRespuesta.setEnabled(true);
            }
        } else {
            btnAgregarRespuesta.setEnabled(false);
        }
    }

    @Override
    public void onOkPressedPreguntaCategorica(NuevaPreguntaCategoricaViewModel viewModel) {
        crearEncuestasModel.agregarPreguntaCategorica(viewModel);
        actualizarListaPreguntasYRespuestas();
        listaPreguntas.setSelectedIndex(listaPreguntas.getModel().getSize() - 1);
    }

    @Override
    public void onOkPressedPreguntaNumerica(NuevaPreguntaNumericaViewModel viewModel) {
        crearEncuestasModel.agregarPreguntaNumerica(viewModel);
        actualizarListaPreguntasYRespuestas();
        listaPreguntas.setSelectedIndex(listaPreguntas.getModel().getSize() - 1);
    }

    @Override
    public void onOkPressedPreguntaTexto(NuevaPreguntaTextoViewModel viewModel) {
        crearEncuestasModel.agregarPreguntaTexto(viewModel);
        actualizarListaPreguntasYRespuestas();
        listaPreguntas.setSelectedIndex(listaPreguntas.getModel().getSize() - 1);
    }

    @Override
    public void onOkPressedPreguntaMultiple(NuevaPreguntaMultipleViewModel viewModel) {
        crearEncuestasModel.agregarPreguntaMultiple(viewModel);
        actualizarListaPreguntasYRespuestas();
        listaPreguntas.setSelectedIndex(listaPreguntas.getModel().getSize() - 1);
    }

    @Override
    public void onOkPressedPreguntaMixta(NuevaPreguntaMixtaViewModel viewModel) {
        crearEncuestasModel.agregarPreguntaMixta(viewModel);
        actualizarListaPreguntasYRespuestas();
        listaPreguntas.setSelectedIndex(listaPreguntas.getModel().getSize() - 1);
    }

    @Override
    public void onCancelPressed() {
        // Nothing to do here, the dialog is just closed.
    }

    @Subscribe
    public void onCreateEncuesta(CreateEncuestaEvent createEncuestaEvent) {
        if (createEncuestaEvent.isSuccess()) {
            JOptionPane.showMessageDialog(null, "La encuesta se ha creado con exito", "Exito!", JOptionPane.INFORMATION_MESSAGE);
            crearEncuestasFrame.clearAll();
        } else {
            System.out.println(createEncuestaEvent.getErrorMessage());
        }
        crearEncuestasFrame.hideLoadingDialog();
    }

    public void closeCrearEncuestasFrameWindow() {
        if (crearEncuestasFrame != null) {
            crearEncuestasFrame.dispose();
        }
    }
}

