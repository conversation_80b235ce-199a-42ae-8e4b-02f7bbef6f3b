package ar.com.oikosanalistas.admin.ui.model;

import ar.com.oikosanalistas.admin.events.DeleteEncuestaEvent;
import ar.com.oikosanalistas.admin.events.DeleteEncuestaUserEvent;
import ar.com.oikosanalistas.admin.events.ListAllEncuestasEvent;
import ar.com.oikosanalistas.admin.events.UpdateEncuestasEvent;
import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import ar.com.oikosanalistas.admin.ui.model.pojo.Usuario;
import com.google.api.core.ApiFutureCallback;
import com.google.api.core.ApiFutures;
import com.google.firebase.database.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static ar.com.oikosanalistas.admin.utils.Constants.ENCUESTAS_PATH;
import static com.google.common.util.concurrent.MoreExecutors.directExecutor;

public class VerEncuestasModel extends BaseModel {

    public VerEncuestasModel() {
    }

    public void listAllEncuestas() {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference(ENCUESTAS_PATH);

        ref.addListenerForSingleValueEvent(new ValueEventListener() {
            @Override
            public void onDataChange(DataSnapshot dataSnapshot) {
                List<Encuesta> encuestaList = new ArrayList<>();
                for (DataSnapshot children : dataSnapshot.getChildren()) {
                    Encuesta encuesta = children.getValue(Encuesta.class);
                    encuesta.setId(children.getKey());
                    encuestaList.add(encuesta);
                }

                postBus(new ListAllEncuestasEvent(encuestaList));
            }

            @Override
            public void onCancelled(DatabaseError error) {
                System.out.println("Error obteniendo la lista de encuestas");
                postBus(new ListAllEncuestasEvent("Error obteniendo la lista de encuestas", error.toException()));
            }
        });
    }

    public void saveUpdatedEncuesta(Encuesta encuesta, Map<String, Object> map) {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference(ENCUESTAS_PATH);
        ref = ref.child(encuesta.getId());

        ApiFutures.addCallback(ref.updateChildrenAsync(map), new ApiFutureCallback<Void>() {
            @Override
            public void onSuccess(Void result) {
                postBus(new UpdateEncuestasEvent("Encuesta actualizada"));
            }

            @Override
            public void onFailure(Throwable t) {
                postBus(new UpdateEncuestasEvent("Error actualizando encuesta", t));
            }
        }, directExecutor());
    }

    public void deleteEncuesta(String id) {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference("");
        DatabaseReference encuestasRef = ref.child("encuestas/"+id);
        ApiFutures.addCallback(encuestasRef.removeValueAsync(), new ApiFutureCallback<Void>() {

            @Override
            public void onFailure(Throwable t) {
                postBus(new DeleteEncuestaEvent("Error al eliminar la Encuesta", t));
            }

            @Override
            public void onSuccess(Void result) {
                postBus(new DeleteEncuestaEvent(id));
            }
        }, directExecutor());
    }

    public void borrarEncuestaDeUsuarios(String idEncuesta) {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference("");
        DatabaseReference usuariosRef = ref.child("usuarios");

        usuariosRef.addListenerForSingleValueEvent(new ValueEventListener() {
            @Override
            public void onDataChange(DataSnapshot dataSnapshot) {
                GenericTypeIndicator<Map<String, Usuario>> t = new GenericTypeIndicator<Map<String, Usuario>>() {};
                Map<String, Usuario> usuarioMap = dataSnapshot.getValue(t);
                Map<String, Object> usuarioNuevoMap = new HashMap<>();
                for (Map.Entry<String, Usuario> entry : usuarioMap.entrySet()) {
                    Usuario nuevoUsuario = entry.getValue();
                    nuevoUsuario.getEncuestasAsignadas().remove(idEncuesta);

                    usuarioNuevoMap.put(entry.getKey(),nuevoUsuario);
                }
                ApiFutures.addCallback(usuariosRef.updateChildrenAsync(usuarioNuevoMap), new ApiFutureCallback<Void>() {
                    @Override
                    public void onFailure(Throwable t) {
                        postBus(new DeleteEncuestaUserEvent("Error al eliminar la encuesta de los usuarios", t));
                    }

                    @Override
                    public void onSuccess(Void result) {
                        postBus(new DeleteEncuestaUserEvent(idEncuesta));
                    }
                }, directExecutor());
            }

            @Override
            public void onCancelled(DatabaseError error) {
                postBus(new DeleteEncuestaUserEvent("Error al leer los usuarios", error.toException()));
            }
        });
    }
}
