package ar.com.oikosanalistas.admin.ui.controller;

import ar.com.oikosanalistas.admin.events.EditarEncuestaEvent;
import ar.com.oikosanalistas.admin.ui.model.CrearEncuestasModel;
import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import org.greenrobot.eventbus.Subscribe;

import javax.swing.*;

public class EditarEncuestasController extends CrearEncuestasController {

    public EditarEncuestasController(Encuesta encuestaSeleccionada) {
        crearEncuestasModel = new CrearEncuestasModel(encuestaSeleccionada);

        initComponents();
        initListeners();

        crearEncuestasFrame.setModoEdicion(encuestaSeleccionada.getTitulo());
        actualizarUISegunEncuesta();
    }

    private void actualizarUISegunEncuesta() {
        actualizarTitulo();
        actualizarDescripcion();
        actualizarListaPreguntasYRespuestas();
    }

    protected void crearEncuesta() {
        crearEncuestasModel.editarEncuesta();
    }

    @Subscribe
    public void onEditEncuesta(EditarEncuestaEvent editarEncuestaEvent) {
        if (editarEncuestaEvent.isSuccess()) {
            JOptionPane.showMessageDialog(null, "La encuesta se ha editado con exito", "Exito!", JOptionPane.INFORMATION_MESSAGE);
            crearEncuestasFrame.clearAll();
        } else {
            System.out.println(editarEncuestaEvent.getErrorMessage());
        }
        crearEncuestasFrame.hideLoadingDialog();
    }
}
