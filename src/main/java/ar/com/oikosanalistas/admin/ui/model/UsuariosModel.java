package ar.com.oikosanalistas.admin.ui.model;

import ar.com.oikosanalistas.admin.events.*;
import com.google.api.core.ApiFutureCallback;
import com.google.api.core.ApiFutures;
import com.google.firebase.auth.*;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.google.common.util.concurrent.MoreExecutors.directExecutor;

public class UsuariosModel extends BaseModel {

    public UsuariosModel() {

    }

    public void getUserById(String uid) {
        ApiFutures.addCallback(getFirebaseAuth().getUserAsync(uid), new ApiFutureCallback<UserRecord>() {
            @Override
            public void onFailure(Throwable t) {
                postBus(new GetUserByIdEvent("Error descargando usuario según su ID", t));
            }

            @Override
            public void onSuccess(UserRecord result) {
                postBus(new GetUserByIdEvent(result));
            }
        }, directExecutor());
    }

    public void createUser(String displayName, String email, String password) {
        //TODO Revisar los null
//        if (notEmpty(displayName, email, password)) {

        UserRecord.CreateRequest request = new UserRecord.CreateRequest()
                .setDisplayName(displayName)
                .setEmail(email)
                .setEmailVerified(false)
                .setPassword(password)
                .setDisabled(false);

        ApiFutures.addCallback(getFirebaseAuth().createUserAsync(request), new ApiFutureCallback<UserRecord>() {
            @Override
            public void onFailure(Throwable t) {
                postBus(new CreateUserEvent("Error al crear el usuario con nombre " + displayName, t));
            }

            @Override
            public void onSuccess(UserRecord result) {
                postBus(new CreateUserEvent(result));
            }
        }, directExecutor());
//        } else {
//            //TODO Mostrar dialog con mensaje de error
//            System.out.println("Los parametros no pueden ser vacios");
//        }
    }

    public void updateUser(String uid, String displayName, String email, String password) {
        UserRecord.UpdateRequest request = new UserRecord.UpdateRequest(uid)
                .setDisplayName(displayName)
                .setEmail(email);
        if (!password.equals("******")) {
            request.setPassword(password);
        }

        ApiFutures.addCallback(getFirebaseAuth().updateUserAsync(request), new ApiFutureCallback<UserRecord>() {
            @Override
            public void onFailure(Throwable t) {
                postBus(new UpdateUserEvent("Error al actualizar el usuario con ID " + uid, t));
            }

            @Override
            public void onSuccess(UserRecord result) {
                postBus(new UpdateUserEvent(result));
            }
        }, directExecutor());
    }

    public void listAllUsers() {
        ApiFutures.addCallback(getFirebaseAuth().listUsersAsync(null), new ApiFutureCallback<ListUsersPage>() {
            @Override
            public void onFailure(Throwable t) {
                postBus(new ListAllUsersEvent("Error descargando lista de usuarios", t));
            }

            @Override
            public void onSuccess(ListUsersPage page) {
                List<UserInfo> userList = new ArrayList<>();
                while (page != null) {
                    for (ExportedUserRecord user : page.getValues()) {
                        userList.add(user);
                    }
                    page = page.getNextPage();
                }

                postBus(new ListAllUsersEvent(userList));
            }
        }, directExecutor());
    }

    public void saveUser(String uid, String displayName, String email) {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference("");
        DatabaseReference usuariosRef = ref.child("usuarios");
        Map<String, Object> user = new HashMap<>();
        user.put(uid + "/displayName", displayName);
        user.put(uid + "/email", email);


        ApiFutures.addCallback(usuariosRef.updateChildrenAsync(user), new ApiFutureCallback<Void>() {
            @Override
            public void onFailure(Throwable t) {
                postBus(new SaveUserEvent("Error al guardar el Usuario", t));
            }

            @Override
            public void onSuccess(Void result) {
                postBus(new SaveUserEvent(uid));
            }
        }, directExecutor());
    }

    public void saveUpdatedUser(String uid, String displayName, String email) {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference("");
        DatabaseReference usuariosRef = ref.child("usuarios");
        Map<String, Object> userUpdates = new HashMap<>();
        userUpdates.put(uid + "/displayName", displayName);
        userUpdates.put(uid + "/email", email);

        ApiFutures.addCallback(usuariosRef.updateChildrenAsync(userUpdates), new ApiFutureCallback<Void>() {
            @Override
            public void onFailure(Throwable t) {
                postBus(new SaveUpdatedUserEvent("Error al actualizar el Usuario", t));
            }

            @Override
            public void onSuccess(Void result) {
                postBus(new SaveUpdatedUserEvent(uid));
            }
        }, directExecutor());
    }

    public void deleteUser(String uid) {
        ApiFutures.addCallback(getFirebaseAuth().deleteUserAsync(uid), new ApiFutureCallback<Void>() {

            @Override
            public void onFailure(Throwable t) {
                postBus(new DeleteUserEvent("Error al eliminar el Usuario", t));
            }

            @Override
            public void onSuccess(Void result) {
                postBus(new DeleteUserEvent(uid));
            }
        }, directExecutor());
    }

    public void deleteUserDB(String uid) {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference("");
        DatabaseReference usuariosRef = ref.child("usuarios/" + uid);
        ApiFutures.addCallback(usuariosRef.removeValueAsync(), new ApiFutureCallback<Void>() {

            @Override
            public void onFailure(Throwable t) {
                postBus(new DeleteUserDBEvent("Error al eliminar el Usuario", t));
            }

            @Override
            public void onSuccess(Void result) {
                postBus(new DeleteUserDBEvent(uid));
            }
        }, directExecutor());
    }

}
