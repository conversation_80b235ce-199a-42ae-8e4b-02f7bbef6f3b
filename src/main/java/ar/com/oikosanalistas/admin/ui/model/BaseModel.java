package ar.com.oikosanalistas.admin.ui.model;

import com.google.firebase.auth.FirebaseAuth;
import org.greenrobot.eventbus.EventBus;

public class BaseModel {

    private EventBus bus;

    public BaseModel(){
        bus = EventBus.getDefault();
    }

    protected FirebaseAuth getFirebaseAuth() {
        return FirebaseAuth.getInstance();
    }

    protected EventBus getBus() {
        return bus;
    }

    protected void postBus(Object event){
        bus.post(event);
    }

}
