package ar.com.oikosanalistas.admin.ui.model;

import ar.com.oikosanalistas.admin.ui.model.pojo.Usuario;

import javax.swing.*;
import java.util.ArrayList;

public class UserEncuestaListModel extends AbstractListModel {

    private ArrayList<Usuario> usersList = new ArrayList<>();

    @Override
    public int getSize() {
        return usersList.size();
    }

    /**
     * Devuelve el elemento de la lista en la posición index
     *
     * @param index posición actual
     * @return objeto en la posición recibida por parámetro
     */
    @Override
    public Object getElementAt(int index) {
        Usuario user = usersList.get(index);
        return user.getDisplayName();
    }

    public void addUser(Usuario user) {
        usersList.add(user);
        this.fireIntervalAdded(this, getSize(), getSize() + 1);
    }

    public void eliminarUser(int index0) {
        usersList.remove(index0);
        this.fireIntervalRemoved(index0, getSize(), getSize() + 1);
    }

    public Usuario getUser(int index) {
        return usersList.get(index);
    }
}
