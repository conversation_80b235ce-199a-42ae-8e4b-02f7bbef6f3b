package ar.com.oikosanalistas.admin.ui.view;

import javax.swing.*;
import java.awt.*;

public abstract class BaseFrame extends JFrame {

    private JDialog loadingDialog;

    public BaseFrame(){
        initializeDialog();
    }

    private void initializeDialog() {
        loadingDialog = new JDialog(this, "Cargando", Dialog.ModalityType.MODELESS);
        
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        JLabel loadingLabel = new JLabel("Cargando datos, por favor espere...");
        loadingLabel.setHorizontalAlignment(SwingConstants.CENTER);

        JProgressBar progressBar = new JProgressBar();
        progressBar.setIndeterminate(true);

        panel.add(loadingLabel, BorderLayout.NORTH);
        panel.add(progressBar, BorderLayout.CENTER);

        loadingDialog.setUndecorated(true);
        loadingDialog.getContentPane().add(panel);
        loadingDialog.pack();
        loadingDialog.setDefaultCloseOperation(JDialog.DO_NOTHING_ON_CLOSE);
        loadingDialog.setResizable(false);
    }

    abstract JPanel getPanel();

    public void showLoadingDialog() {
        setEnabled(false);
        loadingDialog.setLocationRelativeTo(this);
        loadingDialog.setVisible(true);
    }

    public void hideLoadingDialog() {
        if (loadingDialog != null && loadingDialog.isVisible()) {
            loadingDialog.setVisible(false);
        }
        setEnabled(true);
    }

    public void showMessageDialog(String message) {
        JOptionPane.showMessageDialog(this, message);
    }
}
