package ar.com.oikosanalistas.admin.ui.view.viewmodel;

import ar.com.oikosanalistas.admin.ui.view.BaseDialog;

public class NuevaPreguntaNumericaViewModel implements BaseDialog.PreguntaDialogViewModel {

    private String descripcion;
    private int minValue;
    private int maxValue;

    public String getDescripcion() {
        return descripcion;
    }

    public int getMinValue() {
        return minValue;
    }

    public int getMaxValue() {
        return maxValue;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    public void setMinValue(int minValue) {
        this.minValue = minValue;
    }

    public void setMaxValue(int maxValue) {
        this.maxValue = maxValue;
    }
}
