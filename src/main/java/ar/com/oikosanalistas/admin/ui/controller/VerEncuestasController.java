package ar.com.oikosanalistas.admin.ui.controller;

import ar.com.oikosanalistas.admin.events.DeleteEncuestaEvent;
import ar.com.oikosanalistas.admin.events.DeleteEncuestaUserEvent;
import ar.com.oikosanalistas.admin.events.ListAllEncuestasEvent;
import ar.com.oikosanalistas.admin.events.UpdateEncuestasEvent;
import ar.com.oikosanalistas.admin.ui.model.EncuestasListModel;
import ar.com.oikosanalistas.admin.ui.model.VerEncuestasModel;
import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import ar.com.oikosanalistas.admin.ui.model.pojo.pregunta.Pregunta;
import ar.com.oikosanalistas.admin.ui.model.pojo.respuesta.Respuesta;
import ar.com.oikosanalistas.admin.ui.view.VerEncuestasFrame;
import ar.com.oikosanalistas.admin.utils.Constants;
import com.google.api.client.util.ArrayMap;
import org.greenrobot.eventbus.Subscribe;

import javax.swing.*;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Map;

import static ar.com.oikosanalistas.admin.utils.StringUtils.isANumber;
import static ar.com.oikosanalistas.admin.utils.StringUtils.isEmpty;

public class VerEncuestasController extends BaseController {

    private VerEncuestasFrame verEncuestasFrame;
    private VerEncuestasModel verEncuestasModel;
    private JList listEncuestas;
    private JList listPreguntas;
    private JList listRespuestas;
    private JButton btnEliminarEncuesta;
    private JButton btnClonarEncuesta;
    private JButton btnConfigurarFiltros;
    private JButton btnGuardarEncuesta;
    private Map<String, Object> map;

    public VerEncuestasController() {
        verEncuestasModel = new VerEncuestasModel();

        initComponents();
        initListeners();

        initSiguientesPreguntasMap();
    }

    public void showVerEncuestasFrameWindow() {
        actualizarListaEncuestas();
        verEncuestasFrame.setVisible(true);
    }

    private void initComponents() {
        verEncuestasFrame = new VerEncuestasFrame();
        listEncuestas = verEncuestasFrame.getListEncuestas();
        listPreguntas = verEncuestasFrame.getListPreguntas();
        listRespuestas = verEncuestasFrame.getListRespuestas();
        btnEliminarEncuesta = verEncuestasFrame.getBtnEliminarEncuesta();
        btnClonarEncuesta = verEncuestasFrame.getBtnClonarEncuesta();
        btnConfigurarFiltros = verEncuestasFrame.getBtnConfigurarFiltros();
        btnGuardarEncuesta = verEncuestasFrame.getBtnGuardarEncuesta();
        setEnabledBtnConfigurarFiltros(false);
    }

    private void initListeners() {
        btnEliminarEncuesta.addActionListener(new ButtonEliminarEncuestaListener());
        btnClonarEncuesta.addActionListener(new ButtonClonarEncuestaListener());
        listEncuestas.addListSelectionListener(new EncuestaListSelectionListener());
        listPreguntas.addListSelectionListener(new PreguntaListSelectionListener());
        listRespuestas.addListSelectionListener(new RespuestasListSelectionListener());
        btnConfigurarFiltros.addActionListener(new ButtonConfigurarFiltrosListener());
        btnGuardarEncuesta.addActionListener(new ButtonGuardarEncuestaListener());
    }

    private void actualizarListaEncuestas() {
        verEncuestasFrame.showLoadingDialog();
        verEncuestasModel.listAllEncuestas();
    }

    public void closeVerEncuestasFrameWindow() {
        if (verEncuestasFrame != null) {
            verEncuestasFrame.dispose();
        }
    }

    private void setEnabledBtnConfigurarFiltros(boolean enabled) {
        btnConfigurarFiltros.setEnabled(enabled);
    }

    private class ButtonEliminarEncuestaListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent actionEvent) {
            if (listEncuestas.isSelectionEmpty()) {
                JOptionPane.showMessageDialog(null, "Para eliminar una encuesta primero debe seleccionar una de la lista", "ERROR al eliminar", JOptionPane.ERROR_MESSAGE);

            } else {
                int selection = listEncuestas.getSelectedIndex();
                EncuestasListModel encuestasList = verEncuestasFrame.getEncuestasListModel();
                Encuesta encuesta = encuestasList.getEncuestaAt(selection);
                int resp = JOptionPane.showConfirmDialog(null, "¿Está seguro de que quiere eliminar la encuesta: "+encuesta.getTitulo()+" ?", "Alerta!", JOptionPane.YES_NO_OPTION);
                if(resp == 0){
                    verEncuestasFrame.showLoadingDialog();
                    verEncuestasModel.deleteEncuesta(encuesta.getId());
                }
            }
        }
    }

    private class ButtonClonarEncuestaListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent actionEvent) {
            if (listEncuestas.isSelectionEmpty()) {
                JOptionPane.showMessageDialog(null, "Para clonar una encuesta primero debe seleccionar una de la lista", "ERROR al intentar clonar", JOptionPane.ERROR_MESSAGE);

            } else {
                int selection = listEncuestas.getSelectedIndex();
                EncuestasListModel encuestasList = verEncuestasFrame.getEncuestasListModel();
                Encuesta encuesta = encuestasList.getEncuestaAt(selection);
                String newTitle = JOptionPane.showInputDialog(null, "Nombre de la nueva encuesta:", "Clonar encuesta", JOptionPane.QUESTION_MESSAGE);

                if(!newTitle.isEmpty()){
                    verEncuestasFrame.dispose();
                    encuesta.setTitulo(newTitle);
                    ClonarEncuestasController clonarEncuestasController = new ClonarEncuestasController(encuesta);
                    clonarEncuestasController.showFrameWindow();
                }
            }
        }
    }

    private class ButtonGuardarEncuestaListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent actionEvent) {
            verEncuestasModel.saveUpdatedEncuesta(
                    verEncuestasFrame.getEncuestasListModel().getEncuestaAt(listEncuestas.getSelectedIndex()),
                    map
            );
        }
    }

    private class ButtonConfigurarFiltrosListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String input = (String) JOptionPane.showInputDialog(
                    verEncuestasFrame,
                    "Si para la pregunta \n'" + getSelectedPregunta().getDescripcion() + "'\n"
                            + "responden '" + getSelectedRespuesta().getDescripcion() + "'\n"
                            + "entonces ir a la pregunta número:\n",
                    "Pregunta siguiente según respuesta",
                    JOptionPane.QUESTION_MESSAGE,
                    null,
                    null,
                    selectedRespuestaHasSiguientePregunta() ? getSelectedRespuesta().getSiguientePregunta() + "" : "");

            if (isEmpty(input)) {
                getSelectedRespuesta().setSiguientePregunta(null);
            } else if (isANumber(input) && isValidSiguientePregunta(Integer.valueOf(input))) {
                getSelectedRespuesta().setSiguientePregunta(Integer.valueOf(input));

                map.put("preguntasList/"
                                + getSelectedPreguntaIndex()
                                + "/posiblesRespuestasCategoricas/"
                                + getSelectedRespuestaIndex()
                                + "/siguientePregunta",
                        Integer.valueOf(input));
            }
        }
    }

    private class EncuestaListSelectionListener implements ListSelectionListener {
        public void valueChanged(ListSelectionEvent e) {
            if (e.getValueIsAdjusting()) {
                int selection = listEncuestas.getSelectedIndex();
                EncuestasListModel encuestasList = verEncuestasFrame.getEncuestasListModel();

                Encuesta encuesta = encuestasList.getEncuestaAt(selection);
                verEncuestasFrame.clearListRespuestas();
                verEncuestasFrame.actualizarListaPreguntas(encuesta.getPreguntasList());
            }
        }
    }

    private class PreguntaListSelectionListener implements ListSelectionListener {
        public void valueChanged(ListSelectionEvent e) {
            if (e.getValueIsAdjusting()) {
                Pregunta pregunta = getSelectedPregunta();

                if (pregunta.getTipo().equals(Constants.TEXTO)) {
                    verEncuestasFrame.clearListRespuestas();
                } else {
                    verEncuestasFrame.actualizarListaRespuestas(pregunta.getPosiblesRespuestasCategoricas());
                }
            }
        }
    }

    private class RespuestasListSelectionListener implements ListSelectionListener {
        @Override
        public void valueChanged(ListSelectionEvent e) {
            if (e.getValueIsAdjusting()) {
                Pregunta preg = getSelectedPregunta();
                boolean activarBtnFiltro = isSomeRespuestaSelected() && isPreguntaCategorica(preg);

                setEnabledBtnConfigurarFiltros(activarBtnFiltro);
            }
        }
    }

    private boolean isSomeRespuestaSelected() {
        return listRespuestas.getSelectedIndex() >= 0;
    }

    private Integer getSelectedPreguntaIndex() {
        return listPreguntas.getSelectedIndex();
    }

    private Pregunta getSelectedPregunta() {
        int position = listPreguntas.getSelectedIndex();
        return verEncuestasFrame.getPreguntasListModel().getPreguntaAt(position);
    }

    private Integer getSelectedRespuestaIndex() {
        return listRespuestas.getSelectedIndex();
    }

    private Respuesta getSelectedRespuesta() {
        int position = listRespuestas.getSelectedIndex();
        return verEncuestasFrame.getRespuestasListModel().getRespuestaAt(position);
    }

    private boolean isPreguntaCategorica(Pregunta preg) {
        return preg.getTipo().equals(Constants.CATEGORICA);
    }

    private boolean selectedRespuestaHasSiguientePregunta() {
        return isEmpty(String.valueOf(getSelectedRespuesta().getSiguientePregunta()));
    }

    private boolean isValidSiguientePregunta(Integer input) {
        return input >= 0 && input < getCantidadPreguntas();
    }

    private Integer getCantidadPreguntas() {
        return verEncuestasFrame.getPreguntasListModel().getSize();
    }

    private void initSiguientesPreguntasMap() {
        map = new ArrayMap<>();
    }

    @Subscribe
    public void onListAllEncuestasEvent(ListAllEncuestasEvent userListEvent) {
        if (userListEvent.isSuccess()) {
            verEncuestasFrame.actualizarListaEncuestas(userListEvent.getResult());
        } else {
            System.out.println(userListEvent.getErrorMessage());
        }
        verEncuestasFrame.hideLoadingDialog();
    }

    @Subscribe
    public void onUpdateEncuestasEvent(UpdateEncuestasEvent event) {
        if (event.isSuccess()) {
            JOptionPane.showMessageDialog(verEncuestasFrame, event.getResult());
            initSiguientesPreguntasMap();
        } else {
            JOptionPane.showMessageDialog(verEncuestasFrame, event.getErrorMessage(), "Ocurrió un error", JOptionPane.ERROR_MESSAGE);
        }
    }

    @Subscribe
    public void onDeleteEncuestaEvent (DeleteEncuestaEvent deleteEncuestaEvent){
        if(deleteEncuestaEvent.isSuccess()){
            verEncuestasModel.borrarEncuestaDeUsuarios(deleteEncuestaEvent.getResult());
        }
        else{
            System.out.println(deleteEncuestaEvent.getErrorMessage());
        }
    }

    @Subscribe
    public void onDeleteEncuestaUserEvent(DeleteEncuestaUserEvent deleteEncuestaUserEvent){
        if(deleteEncuestaUserEvent.isSuccess()){
            if (deleteEncuestaUserEvent.getResult() != null) {
                JOptionPane.showMessageDialog(null, "La Encuesta " + deleteEncuestaUserEvent.getResult() + " se ha eliminado con exito", "Exito!", JOptionPane.INFORMATION_MESSAGE);
            }
            actualizarListaEncuestas();
        }
        else{
            System.out.println(deleteEncuestaUserEvent.getErrorMessage());
        }
        verEncuestasFrame.clearListRespuestas();
        verEncuestasFrame.clearListPreguntas();
        verEncuestasFrame.hideLoadingDialog();
    }
}
