package ar.com.oikosanalistas.admin.ui.model;

import ar.com.oikosanalistas.admin.events.*;
import ar.com.oikosanalistas.admin.ui.model.pojo.DynamicOps;
import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import ar.com.oikosanalistas.admin.ui.model.pojo.EncuestaRealizada;
import ar.com.oikosanalistas.admin.ui.model.pojo.pregunta.Pregunta;
import ar.com.oikosanalistas.admin.utils.DynamicOperationsUtils;
import ar.com.oikosanalistas.admin.utils.EncodeUtils;
import com.google.api.core.ApiFutureCallback;
import com.google.api.core.ApiFutures;
import com.google.firebase.auth.ExportedUserRecord;
import com.google.firebase.auth.ListUsersPage;
import com.google.firebase.auth.UserInfo;
import com.google.firebase.database.*;
import com.google.gson.Gson;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static ar.com.oikosanalistas.admin.utils.Constants.*;
import static com.google.common.util.concurrent.MoreExecutors.directExecutor;

public class GenerarResultadosModel extends BaseModel {

    private List<String> preguntasStringList;
    private DynamicOps dynamicOps;

    public void listAllEncuestasGenerar() {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference(ENCUESTAS_PATH);

        ref.addListenerForSingleValueEvent(new ValueEventListener() {
            @Override
            public void onDataChange(DataSnapshot dataSnapshot) {
                List<Encuesta> encuestaList = new ArrayList<>();
                for (DataSnapshot children : dataSnapshot.getChildren()) {
                    Encuesta encuesta = children.getValue(Encuesta.class);
                    encuesta.setId(children.getKey());
                    encuestaList.add(encuesta);
                }

                postBus(new ListAllEncuestasGenerarEvent(encuestaList));
            }

            @Override
            public void onCancelled(DatabaseError error) {
                System.out.println("Error obteniendo la lista de encuestas");
                postBus(new ListAllEncuestasGenerarEvent("Error obteniendo la lista de encuestas", error.toException()));
            }
        });
    }

    public void listAllUsersGenerar() {
        ApiFutures.addCallback(getFirebaseAuth().listUsersAsync(null), new ApiFutureCallback<ListUsersPage>() {
            @Override
            public void onFailure(Throwable t) {
                postBus(new ListAllUsersEvent("Error descargando lista de usuarios generar", t));
            }

            @Override
            public void onSuccess(ListUsersPage page) {
                List<UserInfo> userList = new ArrayList<>();
                while (page != null) {
                    for (ExportedUserRecord user : page.getValues()) {
                        userList.add(user);
                    }
                    page = page.getNextPage();
                }

                postBus(new ListAllUsersGenerarEvent(userList));
            }
        }, directExecutor());
    }

    public void listAllEncuestasRealizadas(String encuestaId, List<UserInfo> usersList) {
        List<EncuestaRealizada> encuestasRealizadasList = new ArrayList<>();
        List<DatabaseReference> pendingRequests = new ArrayList<>();

        final FirebaseDatabase database = FirebaseDatabase.getInstance();

        //generate all the desired requests
        for (UserInfo user : usersList) {
            DatabaseReference ref = database.getReference(ENCUESTAS_REALIZADAS_PATH + "/" + encuestaId + "/" + user.getUid());
            pendingRequests.add(ref);
        }

        //let's execute the first and the rest will be executed one after another
        DatabaseListener firstListener = new DatabaseListener(pendingRequests, encuestasRealizadasList);
        firstListener.executeCurrentRequest();
    }

    public void getEncuestaSegunId(String encuestaId) {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference(ENCUESTAS_PATH + "/" + encuestaId);

        ref.addListenerForSingleValueEvent(new ValueEventListener() {
            @Override
            public void onDataChange(DataSnapshot snapshot) {
                Encuesta encuesta = snapshot.getValue(Encuesta.class);
                encuesta.setId(snapshot.getKey());
                postBus(new GetEncuestaSegunIdEvent(encuesta));
            }

            @Override
            public void onCancelled(DatabaseError error) {
                System.out.println("Error obteniendo la encuesta con ID " + encuestaId);
                postBus(new GetEncuestaSegunIdEvent("Error obteniendo la encuesta con ID " + encuestaId, error.toException()));
            }
        });
    }

    public void setPreguntas(List<Pregunta> preguntasList) {
        preguntasStringList = new ArrayList<>();
        preguntasStringList.add("Nombre encuestador");
        preguntasStringList.add("Hora inicio");
        preguntasStringList.add("Hora fin");
        preguntasStringList.add("Ubicacion");
        preguntasStringList.addAll(convertFromPreguntaToString(preguntasList));
    }

    public List<String> getPreguntasStringList() {
        return preguntasStringList;
    }

    public List<String> getRespuestas(Map<String, String> mapPreguntaRespuesta, EncuestaRealizada encuestaRealizada) {
        List<String> respuestasList = new ArrayList<>();
        respuestasList.add(encuestaRealizada.getNombreEncuestador());
        respuestasList.add(encuestaRealizada.getHoraInicioAsString());
        respuestasList.add(encuestaRealizada.getHoraFinAsString());
        respuestasList.add(encuestaRealizada.getGoogleMapsLink());

        String preguntaAnterior = preguntasStringList.get(0);
        for (int i = respuestasList.size(); i < preguntasStringList.size(); i++) {
            String preguntaActual = preguntasStringList.get(i);
            String respuesta = mapPreguntaRespuesta.get(preguntaActual);
            if (respuesta == null) {
                respuestasList.add(EMPTY_FIELD);
            } else if (esRespuestaMultiple(respuesta) && !preguntaAnterior.equals(preguntaActual)) {
                respuestasList.addAll(extraerRespuestaMultiple(respuesta));
            } else if (!esRespuestaMultiple(respuesta)) {
                respuestasList.add(respuesta);
            }

            preguntaAnterior = preguntaActual;
        }

        return respuestasList;
    }

    private List<String> extraerRespuestaMultiple(String respuesta) {
        List<String> respuestasList = new ArrayList<>(Arrays.asList(respuesta.split(RESPUESTA_MULTIPLE_SEPARATOR)));
        while (respuestasList.contains("")) {
            respuestasList.remove("");
        }

        return respuestasList;
    }

    private boolean esRespuestaMultiple(String respuesta) {
        return respuesta.contains(RESPUESTA_MULTIPLE_NON_SELECTED) || respuesta.contains(RESPUESTA_MULTIPLE_SEPARATOR);
    }

    public void retrieveDynamicOperations() {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference("dynamic_ops");
        ref.addListenerForSingleValueEvent(new ValueEventListener() {
            @Override
            public void onDataChange(DataSnapshot snapshot) {
                dynamicOps = snapshot.getValue(DynamicOps.class);
                DynamicOperationsEvent event = new DynamicOperationsEvent(dynamicOps);
                postBus(event);
            }

            @Override
            public void onCancelled(DatabaseError error) {
                postBus(new DynamicOperationsEvent("No se pudo obtener la URL del JAR de Dynops.", error.toException()));
            }
        });
    }

    public String getLocationsList(List<EncuestaRealizada> encuestasRealizadas) {
        try {
            Class locationOperationClass = DynamicOperationsUtils.getInstance(dynamicOps.getDynops_link()).getLocationOperation();
            Constructor<?> constructor = locationOperationClass.getConstructor();

            Object locationOperationObject = constructor.newInstance();

            Method encuestasRealizadasMethod = locationOperationClass.getMethod(DYNAMIC_OPS_METHOD_SET_DATA, String.class);
            encuestasRealizadasMethod.invoke(locationOperationObject, new Gson().toJson(encuestasRealizadas));

            Method executeMethod = locationOperationClass.getMethod(DYNAMIC_OPS_METHOD_EXECUTE);
            return (String) executeMethod.invoke(locationOperationObject);
        } catch (NoSuchMethodException | IllegalAccessException | InstantiationException | InvocationTargetException | ClassNotFoundException | MalformedURLException e) {
            e.printStackTrace();
        }

        return null;
    }

    public String getHtmlPart1() {
        try {
            Class htmlPart1OperationClass = DynamicOperationsUtils.getInstance(dynamicOps.getDynops_link()).getHtmlPart1Operation();
            Constructor<?> constructor = htmlPart1OperationClass.getConstructor();

            Object htmlPart1OperationObject = constructor.newInstance();

            Method executeMethod = htmlPart1OperationClass.getMethod(DYNAMIC_OPS_METHOD_EXECUTE);
            return (String) executeMethod.invoke(htmlPart1OperationObject);
        } catch (NoSuchMethodException | IllegalAccessException | InstantiationException | InvocationTargetException | ClassNotFoundException | MalformedURLException e) {
            e.printStackTrace();
        }

        return null;
    }

    public String getHtmlPart2() {
        try {
            Class htmlPart2OperationClass = DynamicOperationsUtils.getInstance(dynamicOps.getDynops_link()).getHtmlPart2Operation();
            Constructor<?> constructor = htmlPart2OperationClass.getConstructor();

            Object htmlPart2OperationObject = constructor.newInstance();

            Method executeMethod = htmlPart2OperationClass.getMethod(DYNAMIC_OPS_METHOD_EXECUTE);
            return (String) executeMethod.invoke(htmlPart2OperationObject);
        } catch (NoSuchMethodException | IllegalAccessException | InstantiationException | InvocationTargetException | ClassNotFoundException | MalformedURLException e) {
            e.printStackTrace();
        }

        return null;
    }

    class DatabaseListener {
        private List<DatabaseReference> pendingRequests;
        private List<EncuestaRealizada> encuestasRealizadasList;

        DatabaseListener(List<DatabaseReference> pendingRequests, List<EncuestaRealizada> encuestasRealizadasList) {
            this.pendingRequests = pendingRequests;
            this.encuestasRealizadasList = encuestasRealizadasList;
        }

        void executeCurrentRequest() {
            if (!pendingRequests.isEmpty()) {
                DatabaseReference actualRequest = pendingRequests.get(0);
                pendingRequests.remove(0); //take first element and remove it

                actualRequest.addListenerForSingleValueEvent(new ValueEventListener() {
                    @Override
                    public void onDataChange(DataSnapshot snapshot) {
                        for (DataSnapshot children : snapshot.getChildren()) {
                            //for each EncuestaRealizada for this user we parse it and, if it is not null, we add it to the list
                            EncuestaRealizada encuestaRealizada = children.getValue(EncuestaRealizada.class);
                            if (encuestaRealizada != null) {
                                encuestaRealizada.decodeAndReplaceMapPreguntaRespuesta();
                                encuestasRealizadasList.add(encuestaRealizada);
                            }
                        }

                        //we then execute the remaining requests
                        DatabaseListener nextListener = new DatabaseListener(pendingRequests, encuestasRealizadasList);
                        nextListener.executeCurrentRequest();
                    }

                    @Override
                    public void onCancelled(DatabaseError error) {
                        System.out.println("Error en request");
                    }
                });
            } else {
                //when the list of pending requests is empty then we can fire the event
                if (!encuestasRealizadasList.isEmpty()) {
                    postBus(new ListAllEncuestasRealizadasEvent(encuestasRealizadasList));
                } else {
                    postBus(new ListAllEncuestasRealizadasEvent("Esta encuesta no posee resultados.", new Exception("Esta encuesta no posee resultados.")));
                }
            }
        }
    }

    private List<String> convertFromPreguntaToString(List<Pregunta> preguntasList) {
        List<String> toReturn = new ArrayList<>();
        for (Pregunta pregunta : preguntasList) {
            if (MULTIPLE.equals(pregunta.getTipo())) {
                for (int i = 0; i < pregunta.getPosiblesRespuestasCategoricas().size(); i++) {
                    toReturn.add(pregunta.getDescripcion());
                }
            } else {
                toReturn.add(pregunta.getDescripcion());
            }
        }

        return toReturn;
    }
}
