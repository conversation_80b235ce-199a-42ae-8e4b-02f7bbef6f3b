package ar.com.oikosanalistas.admin.ui.controller;

import ar.com.oikosanalistas.admin.events.*;
import ar.com.oikosanalistas.admin.ui.model.EncuestasListModel;
import ar.com.oikosanalistas.admin.ui.model.AsignarEncuestasModel;
import ar.com.oikosanalistas.admin.ui.model.UserEncuestaListModel;
import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import ar.com.oikosanalistas.admin.ui.model.pojo.Usuario;
import ar.com.oikosanalistas.admin.ui.view.AsignarEncuestasFrame;
import io.sentry.Sentry;
import org.greenrobot.eventbus.Subscribe;

import javax.swing.*;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

public class AsignarEncuestasController extends BaseController {

    private AsignarEncuestasModel asignarEncuestasModel;
    private AsignarEncuestasFrame asignarEncuestasFrame;
    private JList listEncuestas;
    private JList listAddUsuario;
    private JList listDeleteUsuario;
    private JButton btnAddUsuario;
    private JButton btnDeleteUsuario;

    public AsignarEncuestasController() {
        asignarEncuestasModel = new AsignarEncuestasModel();

        initComponents();
        initListeners();
    }

    private void initComponents() {
        asignarEncuestasFrame = new AsignarEncuestasFrame();
        listEncuestas = asignarEncuestasFrame.getListEncuestas();
        listAddUsuario = asignarEncuestasFrame.getListAddUsuario();
        listDeleteUsuario = asignarEncuestasFrame.getListDeleteUsuario();
        btnAddUsuario = asignarEncuestasFrame.getBtnAddUsuario();
        btnDeleteUsuario = asignarEncuestasFrame.getBtnDeleteUsuario();
    }

    private void initListeners(){
        listEncuestas.addListSelectionListener(new EncuestaListSelectionListener());
        btnAddUsuario.addActionListener(new AddLinkListener());
        btnDeleteUsuario.addActionListener(new DeleteLinkListener());
    }


    public void showEncuestasFrameWindow() {
        actualizarListaEncuestas();
        asignarEncuestasFrame.setVisible(true);
    }

    @Subscribe
    public void onUpdateEncuestaEvent(UpdateEncuestasEvent updateEncuestasEvent) {
        if (updateEncuestasEvent.isSuccess()) {
            System.out.println("Encuesta guardada con key " + updateEncuestasEvent.getResult());
        } else {
            Sentry.captureException(new Exception("Error al actualizar encuesta: " + updateEncuestasEvent.getErrorMessage()));
            JOptionPane.showMessageDialog(asignarEncuestasFrame, "Error al actualizar encuesta: " + updateEncuestasEvent.getErrorMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
        asignarEncuestasFrame.hideLoadingDialog();
    }

    @Subscribe
    public void onListAllEncuestasEvent(ListAllEncuestasAsignarEvent userListEvent) {
        if (userListEvent.isSuccess()) {
            asignarEncuestasFrame.actualizarListaEncuestas(userListEvent.getResult());
        } else {
            Sentry.captureException(new Exception("Error al listar encuestas: " + userListEvent.getErrorMessage()));
            JOptionPane.showMessageDialog(asignarEncuestasFrame, "Error al listar encuestas: " + userListEvent.getErrorMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
        asignarEncuestasFrame.hideLoadingDialog();
    }

    @Subscribe
    public void onListAllUsersEncuestaEvent(ListAllUsersEncuestaEvent userListEvent) {
        if (userListEvent.isSuccess()) {
            asignarEncuestasFrame.actualizarListaUsuarios(userListEvent.getResult());
        } else {
            Sentry.captureException(new Exception("Error al listar usuarios de encuesta: " + userListEvent.getErrorMessage()));
            JOptionPane.showMessageDialog(asignarEncuestasFrame, "Error al listar usuarios de encuesta: " + userListEvent.getErrorMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
        asignarEncuestasFrame.hideLoadingDialog();
    }

    private void actualizarListaEncuestas(){
        asignarEncuestasFrame.showLoadingDialog();
        asignarEncuestasModel.listAllEncuestas();
    }

    public void closeEncuestasFrameWindow() {
        if( asignarEncuestasFrame!= null){
            asignarEncuestasFrame.dispose();
        }
    }

    private class EncuestaListSelectionListener implements ListSelectionListener {
        public void valueChanged(ListSelectionEvent e) {
            if(e.getValueIsAdjusting()) {
                asignarEncuestasFrame.showLoadingDialog();
                int selection = listEncuestas.getSelectedIndex();
                EncuestasListModel encuestasList = asignarEncuestasFrame.getEncuestasListModel();

                Encuesta encuesta = encuestasList.getEncuestaAt(selection);
                asignarEncuestasFrame.setTxtDisplayTitulo(encuesta.getTitulo());
                asignarEncuestasFrame.setTxtDisplayDescripcion(encuesta.getDescripcion());

                asignarEncuestasFrame.setEnablebtnAddUsuario();
                asignarEncuestasFrame.setEnablebtnDeleteUsuario();

                asignarEncuestasModel.listLinkUsers(encuesta.getId());
            }

        }
    }

    private class AddLinkListener implements ActionListener{

        @Override
        public void actionPerformed(ActionEvent e) {
            if(listAddUsuario.isSelectionEmpty()){
                JOptionPane.showMessageDialog(null, "Para añadir un usuario primero debe seleccionar uno de la lista", "ERROR al Añadir", JOptionPane.ERROR_MESSAGE);
            }
            else{
                asignarEncuestasFrame.showLoadingDialog();

                int selection = listAddUsuario.getSelectedIndex();
                UserEncuestaListModel userList = asignarEncuestasFrame.getUsuariosListModelAdd();
                Usuario usuario = userList.getUser(selection);

                int selection2 = listEncuestas.getSelectedIndex();
                EncuestasListModel encuestasList = asignarEncuestasFrame.getEncuestasListModel();
                Encuesta encuesta = encuestasList.getEncuestaAt(selection2);

                usuario.getEncuestasAsignadas().add(encuesta.getId());
                encuesta.getEncuestadoresAsignados().add(usuario.getId());


                asignarEncuestasModel.addLink(usuario.getId(), usuario.getEncuestasAsignadas(), encuesta.getId(), encuesta.getEncuestadoresAsignados());
            }
        }
    }

    private class DeleteLinkListener implements ActionListener{

        @Override
        public void actionPerformed(ActionEvent e) {
            if(listDeleteUsuario.isSelectionEmpty()){
                JOptionPane.showMessageDialog(null, "Para eliminar un usuario primero debe seleccionar uno de la lista", "ERROR al Eliminar", JOptionPane.ERROR_MESSAGE);
            }
            else{
                int selection = listDeleteUsuario.getSelectedIndex();
                UserEncuestaListModel userList = asignarEncuestasFrame.getUsuariosListModelDelete();
                Usuario usuario = userList.getUser(selection);

                int selection2 = listEncuestas.getSelectedIndex();
                EncuestasListModel encuestasList = asignarEncuestasFrame.getEncuestasListModel();
                Encuesta encuesta = encuestasList.getEncuestaAt(selection2);

                usuario.getEncuestasAsignadas().remove(encuesta.getId());
                encuesta.getEncuestadoresAsignados().remove(usuario.getId());

                asignarEncuestasModel.deleteLink(usuario.getId(), usuario.getEncuestasAsignadas(), encuesta.getId(), encuesta.getEncuestadoresAsignados());
            }
        }
    }

    @Subscribe
    public void onAddLink(AddLinkUserEncuestaEvent addLinkUserEncuestaEvent){
        if (addLinkUserEncuestaEvent.isSuccess()) {
            if (addLinkUserEncuestaEvent.getResult() != null) {
                JOptionPane.showMessageDialog(null, "El usuario se ha añadido con exito", "Exito!", JOptionPane.INFORMATION_MESSAGE);
            }
            asignarEncuestasModel.listLinkUsers(addLinkUserEncuestaEvent.getResult());
        } else {
            Sentry.captureException(new Exception("Error al añadir link: " + addLinkUserEncuestaEvent.getErrorMessage()));
            JOptionPane.showMessageDialog(asignarEncuestasFrame, "Error al añadir el link: " + addLinkUserEncuestaEvent.getErrorMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
        asignarEncuestasFrame.hideLoadingDialog();
    }

    @Subscribe
    public void onDeleteLink(DeleteLinkUserEncuestaEvent deleteLinkUserEncuestaEvent){
        if (deleteLinkUserEncuestaEvent.isSuccess()) {
            if (deleteLinkUserEncuestaEvent.getResult() != null) {
                JOptionPane.showMessageDialog(null, "El usuario se ha eliminado con exito", "Exito!", JOptionPane.INFORMATION_MESSAGE);
            }
            asignarEncuestasModel.listLinkUsers(deleteLinkUserEncuestaEvent.getResult());
        } else {
            Sentry.captureException(new Exception("Error al eliminar link: " + deleteLinkUserEncuestaEvent.getErrorMessage()));
            JOptionPane.showMessageDialog(asignarEncuestasFrame, "Error al eliminar el link: " + deleteLinkUserEncuestaEvent.getErrorMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
        asignarEncuestasFrame.hideLoadingDialog();
    }
}
