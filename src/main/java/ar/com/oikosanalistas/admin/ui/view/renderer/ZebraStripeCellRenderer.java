package ar.com.oikosanalistas.admin.ui.view.renderer;

import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import ar.com.oikosanalistas.admin.ui.model.pojo.pregunta.Pregunta;
import ar.com.oikosanalistas.admin.ui.model.pojo.respuesta.Respuesta;
import ar.com.oikosanalistas.admin.ui.model.pojo.Usuario;

import javax.swing.*;
import java.awt.*;

public class ZebraStripeCellRenderer extends DefaultListCellRenderer {

    private static final Color ZEBRA_STRIPE_COLOR_LIGHT = new Color(240, 240, 240);
    private static final Color ZEBRA_STRIPE_COLOR_DARK = new Color(45, 45, 45); // A bit lighter than <PERSON><PERSON>'s background

    @Override
    public Component getListCellRendererComponent(JList<?> list, Object value, int index, boolean isSelected, boolean cellHasFocus) {

        // Let the default renderer configure the component with the correct foreground/background for the theme
        Component c = super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);

        // --- Apply custom text formatting based on the object type ---
        if (value instanceof Pregunta) {
            Pregunta pregunta = (Pregunta) value;
            setText("<html><b>" + index + ":</b> " + pregunta.getDescripcion() + " (" + pregunta.getTipo() + ")</html>");
        } else if (value instanceof Respuesta) {
            Respuesta respuesta = (Respuesta) value;
            String toReturn = "<html><b>" + respuesta.getId() + ":</b> " + respuesta.getDescripcion();
            if (respuesta.getSiguientePregunta() != null && respuesta.getSiguientePregunta() >= 0) {
                toReturn += " (-> Preg " + respuesta.getSiguientePregunta() + ")";
            }
            setText(toReturn + "</html>");
        } else if (value instanceof Encuesta) {
            setText(((Encuesta) value).getTitulo());
        } else if (value instanceof Usuario) {
            Usuario usuario = (Usuario) value;
            String text = usuario.getDisplayName();
            if (text == null || text.trim().isEmpty()) {
                text = usuario.getEmail();
            }
            setText(text);
        }

        // --- Apply zebra striping for non-selected items ---
        if (!isSelected) {
            // Check the list's background color at render time to determine the theme
            Color bg = list.getBackground();
            int brightness = (int) (0.299 * bg.getRed() + 0.587 * bg.getGreen() + 0.114 * bg.getBlue());
            boolean isDark = brightness < 128;

            // Set the stripe color only for odd rows
            if (index % 2 != 0) {
                c.setBackground(isDark ? ZEBRA_STRIPE_COLOR_DARK : ZEBRA_STRIPE_COLOR_LIGHT);
            } else {
                c.setBackground(bg); // Use the default background for even rows
            }
            
            // The foreground color is already correctly set by the super() call for the current theme
            c.setForeground(list.getForeground());
        }

        return c;
    }
} 