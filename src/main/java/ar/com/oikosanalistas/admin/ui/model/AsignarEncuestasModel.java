package ar.com.oikosanalistas.admin.ui.model;

import ar.com.oikosanalistas.admin.events.*;
import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import ar.com.oikosanalistas.admin.ui.model.pojo.Usuario;
import com.google.api.core.ApiFutureCallback;
import com.google.api.core.ApiFutures;
import com.google.firebase.database.*;
import ar.com.oikosanalistas.admin.utils.Pair;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static ar.com.oikosanalistas.admin.utils.Constants.ENCUESTAS_PATH;
import static com.google.common.util.concurrent.MoreExecutors.directExecutor;

public class AsignarEncuestasModel extends BaseModel {

    public void listAllEncuestas() {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference(ENCUESTAS_PATH);

        ref.addListenerForSingleValueEvent(new ValueEventListener() {
            @Override
            public void onDataChange(DataSnapshot dataSnapshot) {
                List<Encuesta> encuestaList = new ArrayList<>();
                for (DataSnapshot children : dataSnapshot.getChildren()) {
                    Encuesta encuesta = children.getValue(Encuesta.class);
                    encuesta.setId(children.getKey());
                    encuestaList.add(encuesta);
                }

                postBus(new ListAllEncuestasAsignarEvent(encuestaList));
            }

            @Override
            public void onCancelled(DatabaseError error) {
                System.out.println("Error obteniendo la lista de encuestas");
                postBus(new ListAllEncuestasAsignarEvent("Error obteniendo la lista de encuestas", error.toException()));
            }
        });
    }

    public void listLinkUsers(String idEncuesta){
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference("usuarios");

        ref.addListenerForSingleValueEvent(new ValueEventListener() {
            @Override
            public void onDataChange(DataSnapshot dataSnapshot) {
                GenericTypeIndicator<Map<String, Usuario>> t = new GenericTypeIndicator<Map<String, Usuario>>() {};
                Map<String, Usuario> usuarioMap = dataSnapshot.getValue(t);
                List<Usuario> usuarioList = new ArrayList<>();
                for (Map.Entry<String, Usuario> entry : usuarioMap.entrySet()) {
                    Usuario u = new Usuario(entry.getKey(),entry.getValue().getDisplayName(),entry.getValue().getEmail());
                    for(String encuestaid : entry.getValue().getEncuestasAsignadas()){
                        u.addEncuesta(encuestaid);
                    }
                    usuarioList.add(u);
                }
                postBus(new ListAllUsersEncuestaEvent(new Pair<>(usuarioList,idEncuesta)));
            }

            @Override
            public void onCancelled(DatabaseError error) {
                System.out.println("Error obteniendo la lista de Usuarios");
                postBus(new ListAllUsersEncuestaEvent("Error obteniendo la lista de Usuarios", error.toException()));
            }
        });
    }

    public void deleteLink(String uid, List<String> lista, String idEncuesta, List<String> lista2) {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference("");
        Map<String, Object> user = new HashMap<>();
        user.put("usuarios/"+uid+"/encuestasAsignadas",lista);
        user.put("encuestas/"+idEncuesta+"/encuestadoresAsignados",lista2);

        ApiFutures.addCallback(ref.updateChildrenAsync(user), new ApiFutureCallback<Void>() {
            @Override
            public void onFailure(Throwable t) {
                postBus(new DeleteLinkUserEncuestaEvent("Error al eliminar el Usuario", t));
            }

            @Override
            public void onSuccess(Void result) {
                postBus(new DeleteLinkUserEncuestaEvent(idEncuesta));
            }
        }, directExecutor());

    }

    public void addLink(String uid, List<String> lista, String idEncuesta, List<String> lista2) {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference("");
        Map<String, Object> user = new HashMap<>();
        user.put("usuarios/"+uid+"/encuestasAsignadas",lista);
        user.put("encuestas/"+idEncuesta+"/encuestadoresAsignados",lista2);

        ApiFutures.addCallback(ref.updateChildrenAsync(user), new ApiFutureCallback<Void>() {
            @Override
            public void onFailure(Throwable t) {
                postBus(new AddLinkUserEncuestaEvent("Error al añadir el Usuario", t));
            }

            @Override
            public void onSuccess(Void result) {
                postBus(new AddLinkUserEncuestaEvent(idEncuesta));
            }
        }, directExecutor());
    }
}
