package ar.com.oikosanalistas.admin.ui.model;

import ar.com.oikosanalistas.admin.ui.model.pojo.pregunta.Pregunta;

import javax.swing.*;
import java.util.ArrayList;
import java.util.List;

public class PreguntasListModel extends AbstractListModel<Pregunta> {

    private List<Pregunta> preguntaList;

    public PreguntasListModel(List<Pregunta> preguntaList) {
        this.preguntaList = preguntaList;
    }

    public PreguntasListModel() {
        preguntaList = new ArrayList<>();
    }

    @Override
    public int getSize() {
        return preguntaList == null ? 0 : preguntaList.size();
    }

    @Override
    public Pregunta getElementAt(int index) {
        return preguntaList.get(index);
    }

    public void addPregunta(Pregunta pregunta) {
        int index = preguntaList.size();
        preguntaList.add(pregunta);
        fireIntervalAdded(this, index, index);
    }

    public void clear() {
        int index = preguntaList.size() - 1;
        if (index < 0) {
            return;
        }
        preguntaList.clear();
        fireIntervalRemoved(this, 0, index);
    }

    public Pregunta getPreguntaAt(int index) {
        return preguntaList.get(index);
    }
}
