package ar.com.oikosanalistas.admin.ui.view;

import ar.com.oikosanalistas.admin.utils.Constants;
import com.formdev.flatlaf.FlatDarkLaf;
import com.formdev.flatlaf.FlatLightLaf;
import org.kordamp.ikonli.fontawesome5.FontAwesomeSolid;
import org.kordamp.ikonli.swing.FontIcon;

import javax.swing.*;
import java.awt.*;

public class MainFrame extends BaseFrame {

    private static final int WIDTH = 500;
    private static final int HEIGHT = 500;

    private JPanel mainPanel;
    private JPanel gridPanel;
    private JButton btnShowUsers;
    private JButton btnCrearEncuesta;
    private JButton btnGenerarResultados;
    private JButton btnAsignarEncuestas;
    private JButton btnVerEncuestas;
    private JButton btnCheckVersion;
    private JButton btnEditarEncuestas;


    public MainFrame() {
        setDefaultCloseOperation(WindowConstants.EXIT_ON_CLOSE);
        setTitle(Constants.OIKOS_VERSION);
        setupUI();
        addMenu();
        pack();
        setMinimumSize(getSize());
        setLocationRelativeTo(null);
    }

    private void setupUI() {
        gridPanel = new JPanel(new GridLayout(0, 3, 15, 15));
        gridPanel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));

        // Initialize buttons with icons
        btnGenerarResultados = createMenuButton("Generar Resultados", FontAwesomeSolid.CHART_BAR);
        btnAsignarEncuestas = createMenuButton("Asignar Encuestas", FontAwesomeSolid.USER_CHECK);
        btnCrearEncuesta = createMenuButton("Crear Encuesta", FontAwesomeSolid.PLUS_SQUARE);
        btnShowUsers = createMenuButton("Ver Usuarios", FontAwesomeSolid.USERS);
        btnVerEncuestas = createMenuButton("Ver Encuestas", FontAwesomeSolid.LIST_ALT);
        btnEditarEncuestas = createMenuButton("Editar Encuestas", FontAwesomeSolid.EDIT);
        btnCheckVersion = createMenuButton("Actualizaciones", FontAwesomeSolid.CLOUD_DOWNLOAD_ALT);

        // Add buttons to the grid
        gridPanel.add(btnGenerarResultados);
        gridPanel.add(btnAsignarEncuestas);
        gridPanel.add(btnCrearEncuesta);
        gridPanel.add(btnShowUsers);
        gridPanel.add(btnVerEncuestas);
        gridPanel.add(btnEditarEncuestas);
        gridPanel.add(btnCheckVersion);

        mainPanel = new JPanel(new BorderLayout());
        mainPanel.add(gridPanel, BorderLayout.CENTER);
        setContentPane(mainPanel);
    }

    private JButton createMenuButton(String text, FontAwesomeSolid iconCode) {
        // Use a color that adapts to the current look and feel
        Color iconColor = UIManager.getColor("Button.foreground");
        FontIcon icon = FontIcon.of(iconCode, 40, iconColor);

        // Use HTML to allow for multi-line text, splitting at the space
        String formattedText = "<html><center>" + text.replace(" ", "<br>") + "</center></html>";
        JButton button = new JButton(formattedText, icon);

        button.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 14)); // Adjusted font size for two lines
        button.setVerticalTextPosition(SwingConstants.BOTTOM);
        button.setHorizontalTextPosition(SwingConstants.CENTER);
        button.setPreferredSize(new Dimension(170, 120));

        // Modern look and feel adjustments
        button.setFocusable(false);
        button.setBorderPainted(true);
        button.setContentAreaFilled(true); // Restore hover and pressed effects

        return button;
    }

    private void addMenu() {
        JMenuBar menuBar = new JMenuBar();
        JMenu menu = new JMenu("Apariencia");
        ButtonGroup buttonGroup = new ButtonGroup();

        JRadioButtonMenuItem lightTheme = new JRadioButtonMenuItem("Tema Claro");
        lightTheme.setSelected(true);
        lightTheme.addActionListener(e -> {
            try {
                UIManager.setLookAndFeel(new FlatLightLaf());
                updateUITheme();
            } catch (UnsupportedLookAndFeelException unsupportedLookAndFeelException) {
                unsupportedLookAndFeelException.printStackTrace();
            }
        });


        JRadioButtonMenuItem darkTheme = new JRadioButtonMenuItem("Tema Oscuro");
        darkTheme.addActionListener(e -> {
            try {
                UIManager.setLookAndFeel(new FlatDarkLaf());
                updateUITheme();
            } catch (UnsupportedLookAndFeelException unsupportedLookAndFeelException) {
                unsupportedLookAndFeelException.printStackTrace();
            }
        });

        buttonGroup.add(lightTheme);
        buttonGroup.add(darkTheme);
        menu.add(lightTheme);
        menu.add(darkTheme);
        menuBar.add(menu);
        setJMenuBar(menuBar);
    }

    private void updateUITheme() {
        for (Window window : Window.getWindows()) {
            SwingUtilities.updateComponentTreeUI(window);
            if (window instanceof JFrame || window instanceof JDialog) {
                Dimension size = window.getSize();
                window.pack();
                window.setSize(size);
            }
        }
        updateButtonIcons();
    }

    private void updateButtonIcons() {
        if (gridPanel == null) return;
        Color newIconColor = UIManager.getColor("Button.foreground");

        for (Component component : gridPanel.getComponents()) {
            if (component instanceof JButton) {
                JButton button = (JButton) component;
                if (button.getIcon() instanceof FontIcon) {
                    FontIcon oldIcon = (FontIcon) button.getIcon();
                    FontIcon newIcon = FontIcon.of(oldIcon.getIkon(), oldIcon.getIconSize(), newIconColor);
                    button.setIcon(newIcon);
                }
            }
        }
    }

    @Override
    JPanel getPanel() {
        return mainPanel;
    }

    public JButton getBtnCrearEncuesta() {
        return btnCrearEncuesta;
    }

    public JButton getBtnGenerarResultados() {
        return btnGenerarResultados;
    }

    public JButton getBtnShowUsers() {
        return btnShowUsers;
    }

    public JButton getBtnAsignarEncuestas() { return btnAsignarEncuestas; }

    public JButton getBtnVerEncuestas() { return btnVerEncuestas; }

    public JButton getBtnEditarEncuestas() { return btnEditarEncuestas; }

    public JButton getBtnCheckVersion() { return btnCheckVersion; }
}
