package ar.com.oikosanalistas.admin.ui.view;

import ar.com.oikosanalistas.admin.utils.Constants;

import javax.swing.*;
import java.awt.*;

public class MainFrame extends BaseFrame {

    private static final int WIDTH = 500;
    private static final int HEIGHT = 500;

    private JButton btnShowUsers;
    private JPanel mainPanel;
    private JButton btnCrearEncuesta;
    private JButton btnGenerarResultados;
    private JButton btnAsignarEncuestas;
    private JButton btnVerEncuestas;
    private JButton btnCheckVersion;
    private JButton btnEditarEncuestas;


    public MainFrame() {
        setSize(WIDTH, HEIGHT);
        setContentPane(mainPanel);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(WindowConstants.EXIT_ON_CLOSE);
        setTitle(Constants.OIKOS_VERSION);
    }

    @Override
    JPanel getPanel() {
        return mainPanel;
    }

    public JButton getBtnCrearEncuesta() {
        return btnCrearEncuesta;
    }

    public JButton getBtnGenerarResultados() {
        return btnGenerarResultados;
    }

    public JButton getBtnShowUsers() {
        return btnShowUsers;
    }

    public JButton getBtnAsignarEncuestas() { return btnAsignarEncuestas; }

    public JButton getBtnVerEncuestas() { return btnVerEncuestas; }

    public JButton getBtnEditarEncuestas() { return btnEditarEncuestas; }

    public JButton getBtnCheckVersion() { return btnCheckVersion; }

    {
// GUI initializer generated by IntelliJ IDEA GUI Designer
// >>> IMPORTANT!! <<<
// DO NOT EDIT OR ADD ANY CODE HERE!
        $$$setupUI$$$();
    }

    /**
     * Method generated by IntelliJ IDEA GUI Designer
     * >>> IMPORTANT!! <<<
     * DO NOT edit this method OR call it in your code!
     *
     * @noinspection ALL
     */
    private void $$$setupUI$$$() {
        mainPanel = new JPanel();
        mainPanel.setLayout(new com.intellij.uiDesigner.core.GridLayoutManager(7, 1, new Insets(0, 0, 0, 0), -1, -1));

        // Create all buttons
        btnGenerarResultados = new JButton();
        btnGenerarResultados.setText("Generar Resultados");
        btnGenerarResultados.setFont(new Font(Font.DIALOG, Font.PLAIN, 16));
        mainPanel.add(btnGenerarResultados, new com.intellij.uiDesigner.core.GridConstraints(0, 0, 1, 1, com.intellij.uiDesigner.core.GridConstraints.ANCHOR_CENTER, com.intellij.uiDesigner.core.GridConstraints.FILL_HORIZONTAL, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_SHRINK | com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_GROW, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_FIXED, null, null, null, 0, false));

        btnAsignarEncuestas = new JButton();
        btnAsignarEncuestas.setText("Asignar Encuestas");
        btnAsignarEncuestas.setFont(new Font(Font.DIALOG, Font.PLAIN, 16));
        mainPanel.add(btnAsignarEncuestas, new com.intellij.uiDesigner.core.GridConstraints(1, 0, 1, 1, com.intellij.uiDesigner.core.GridConstraints.ANCHOR_CENTER, com.intellij.uiDesigner.core.GridConstraints.FILL_HORIZONTAL, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_SHRINK | com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_GROW, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_FIXED, null, null, null, 0, false));

        btnCrearEncuesta = new JButton();
        btnCrearEncuesta.setText("Crear Nueva Encuesta");
        btnCrearEncuesta.setFont(new Font(Font.DIALOG, Font.PLAIN, 16));
        mainPanel.add(btnCrearEncuesta, new com.intellij.uiDesigner.core.GridConstraints(2, 0, 1, 1, com.intellij.uiDesigner.core.GridConstraints.ANCHOR_CENTER, com.intellij.uiDesigner.core.GridConstraints.FILL_HORIZONTAL, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_SHRINK | com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_GROW, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_FIXED, null, null, null, 0, false));

        btnShowUsers = new JButton();
        btnShowUsers.setText("Ver Usuarios");
        btnShowUsers.setFont(new Font(Font.DIALOG, Font.PLAIN, 16));
        mainPanel.add(btnShowUsers, new com.intellij.uiDesigner.core.GridConstraints(3, 0, 1, 1, com.intellij.uiDesigner.core.GridConstraints.ANCHOR_CENTER, com.intellij.uiDesigner.core.GridConstraints.FILL_HORIZONTAL, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_SHRINK | com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_GROW, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_FIXED, null, null, null, 0, false));

        btnVerEncuestas = new JButton();
        btnVerEncuestas.setText("Ver Encuestas");
        btnVerEncuestas.setFont(new Font(Font.DIALOG, Font.PLAIN, 16));
        mainPanel.add(btnVerEncuestas, new com.intellij.uiDesigner.core.GridConstraints(4, 0, 1, 1, com.intellij.uiDesigner.core.GridConstraints.ANCHOR_CENTER, com.intellij.uiDesigner.core.GridConstraints.FILL_HORIZONTAL, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_SHRINK | com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_GROW, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_FIXED, null, null, null, 0, false));

        btnEditarEncuestas = new JButton();
        btnEditarEncuestas.setText("Editar Encuestas");
        btnEditarEncuestas.setFont(new Font(Font.DIALOG, Font.PLAIN, 16));
        mainPanel.add(btnEditarEncuestas, new com.intellij.uiDesigner.core.GridConstraints(5, 0, 1, 1, com.intellij.uiDesigner.core.GridConstraints.ANCHOR_CENTER, com.intellij.uiDesigner.core.GridConstraints.FILL_HORIZONTAL, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_SHRINK | com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_GROW, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_FIXED, null, null, null, 0, false));

        btnCheckVersion = new JButton();
        btnCheckVersion.setText("Comprobar actualizaciones Oikos");
        btnCheckVersion.setFont(new Font(Font.DIALOG, Font.PLAIN, 16));
        mainPanel.add(btnCheckVersion, new com.intellij.uiDesigner.core.GridConstraints(6, 0, 1, 1, com.intellij.uiDesigner.core.GridConstraints.ANCHOR_CENTER, com.intellij.uiDesigner.core.GridConstraints.FILL_HORIZONTAL, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_SHRINK | com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_CAN_GROW, com.intellij.uiDesigner.core.GridConstraints.SIZEPOLICY_FIXED, null, null, null, 0, false));
    }

    /**
     * @noinspection ALL
     */
    public JComponent $$$getRootComponent$$$() {
        return mainPanel;
    }
}
