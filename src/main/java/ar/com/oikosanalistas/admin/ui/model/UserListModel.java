package ar.com.oikosanalistas.admin.ui.model;

import com.google.firebase.auth.UserInfo;

import javax.swing.*;
import java.util.ArrayList;

public class UserListModel extends AbstractListModel {

    private ArrayList<UserInfo> usersList = new ArrayList<>();

    @Override
    public int getSize() {
        return usersList.size();
    }

    /**
     * Devuelve el elemento de la lista en la posición index
     *
     * @param index posición actual
     * @return objeto en la posición recibida por parámetro
     */
    @Override
    public Object getElementAt(int index) {
        UserInfo user = usersList.get(index);
        return user.getEmail();
    }

    public void addUser(UserInfo user) {
        usersList.add(user);
        this.fireIntervalAdded(this, getSize(), getSize() + 1);
    }

    public void eliminarUser(int index0) {
        usersList.remove(index0);
        this.fireIntervalRemoved(index0, getSize(), getSize() + 1);
    }

    public UserInfo getUser(int index) {
        if (index < usersList.size()) {
            return usersList.get(index);
        }
        return null;
    }
}
