package ar.com.oikosanalistas.admin.ui.view.viewmodel;

import ar.com.oikosanalistas.admin.ui.view.BaseDialog;

import java.util.ArrayList;
import java.util.List;

public class NuevaPreguntaMultipleViewModel implements BaseDialog.PreguntaDialogViewModel{

    private String descripcion;
    private List<String> respuestas;

    public NuevaPreguntaMultipleViewModel() {
        respuestas = new ArrayList<>();
    }

    public List<String> getRespuestas() {
        return respuestas;
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    public void setRespuestas(List<String> respuestas) {
        this.respuestas = respuestas;
    }

    public void addRespuesta(String respuesta) {
        if (respuestas == null) {
            respuestas = new ArrayList<>();
        }

        respuestas.add(respuesta);
    }
}
