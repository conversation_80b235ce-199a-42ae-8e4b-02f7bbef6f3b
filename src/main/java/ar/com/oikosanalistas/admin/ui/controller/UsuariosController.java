package ar.com.oikosanalistas.admin.ui.controller;

import ar.com.oikosanalistas.admin.events.*;
import ar.com.oikosanalistas.admin.ui.model.UsuariosModel;
import ar.com.oikosanalistas.admin.ui.view.UsuariosFrame;
import ar.com.oikosanalistas.admin.ui.model.UserListModel;
import ar.com.oikosanalistas.admin.utils.ValidatorUtils;
import com.google.firebase.auth.UserInfo;
import com.google.firebase.auth.UserRecord;
import io.sentry.Sentry;
import org.greenrobot.eventbus.Subscribe;

import javax.swing.*;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import static ar.com.oikosanalistas.admin.utils.StringUtils.isEmpty;

public class UsuariosController extends BaseController {

    private UsuariosModel usuariosModel;
    private UsuariosFrame usuariosFrame;
    private JButton btnGuardarUsuario;
    private JList listUsuarios;
    private JButton btnEliminarUsuario;
    private JButton btnLimpiarSeleccion;

    public UsuariosController() {
        usuariosModel = new UsuariosModel();

        initComponents();
        initListeners();

    }

    private void initComponents() {
        usuariosFrame = new UsuariosFrame();
        btnGuardarUsuario = usuariosFrame.getBtnGuardarUsuario();
        listUsuarios = usuariosFrame.getListUsuarios();
        btnEliminarUsuario = usuariosFrame.getBtnEliminarUsuario();
        btnLimpiarSeleccion = usuariosFrame.getBtnLimpiarSeleccion();
    }

    private void initListeners() {
        btnGuardarUsuario.addActionListener(new ButtonGuardarUsuarioListener());
        listUsuarios.addListSelectionListener(new UserListSelectionListener());
        btnEliminarUsuario.addActionListener(new ButtonEliminarUsuarioListener());
        btnLimpiarSeleccion.addActionListener(new ButtonLimpiarSeleccionListener());
    }

    public void showUsuariosFrameWindow() {
        actualizarListaUsuarios();
        usuariosFrame.setVisible(true);
    }

    public void closeUsuariosFrameWindow() {
        if( usuariosFrame!= null){
            usuariosFrame.dispose();
        }
    }

    private class ButtonGuardarUsuarioListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent actionEvent) {

            String nombre = usuariosFrame.getTxtDisplayName();
            String email = usuariosFrame.getTxtEmail();
            String pass = usuariosFrame.getTxtPassword();
            if(nombre.isEmpty()){
                JOptionPane.showMessageDialog(null, "El nombre está vacio", "ERROR!", JOptionPane.ERROR_MESSAGE );
            }
            else if (email.isEmpty()){
                JOptionPane.showMessageDialog(null, "El mail está vacio", "ERROR!", JOptionPane.ERROR_MESSAGE );
            }
            else if (pass.isEmpty()){
                JOptionPane.showMessageDialog(null, "La contraseña está vacia", "ERROR!", JOptionPane.ERROR_MESSAGE );
            }
            else if(!ValidatorUtils.validarNombre(nombre) ){
                JOptionPane.showMessageDialog(null, "El nombre de usuario solo debe contener letras", "ERROR!", JOptionPane.ERROR_MESSAGE );
            }
            else if(!ValidatorUtils.validarEmail(email) ){
                JOptionPane.showMessageDialog(null, "El email es invalido", "ERROR!", JOptionPane.ERROR_MESSAGE );
            }
            else if(!ValidatorUtils.validarPassword(pass) && listUsuarios.isSelectionEmpty()){
                JOptionPane.showMessageDialog(null, "La contraseña debe tener al menos una minuscula, una mayuscula, un numero y tener longitud entre 6 y 20 caracteres", "ERROR!", JOptionPane.ERROR_MESSAGE );
            }else if (listUsuarios.isSelectionEmpty()) {
                usuariosFrame.showLoadingDialog();
                usuariosModel.createUser(nombre, email, pass);
            } else {
                int selection = listUsuarios.getSelectedIndex();
                UserListModel userList = usuariosFrame.getUserListModel();
                UserInfo usuario = userList.getUser(selection);
                if(!pass.equals("******") ) {
                    if (!ValidatorUtils.validarPassword(pass)) {
                        JOptionPane.showMessageDialog(null, "La contraseña debe tener al menos una minuscula, una mayuscula, un numero y tener longitud entre 6 y 20 caracteres", "ERROR!", JOptionPane.ERROR_MESSAGE);

                    }else{
                        usuariosFrame.showLoadingDialog();
                        usuariosModel.updateUser(usuario.getUid(), nombre, email, pass);
                    }
                }
                else {
                    usuariosFrame.showLoadingDialog();
                    usuariosModel.updateUser(usuario.getUid(), nombre, email, pass);
                }
            }
        }
    }

    private class ButtonEliminarUsuarioListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent actionEvent) {

            if (listUsuarios.isSelectionEmpty()) {
                JOptionPane.showMessageDialog(null, "Para eliminar un usuario primero debe seleccionar uno de la lista", "ERROR al eliminar", JOptionPane.ERROR_MESSAGE);

            } else {
                int selection = listUsuarios.getSelectedIndex();
                UserListModel userList = usuariosFrame.getUserListModel();
                UserInfo usuario = userList.getUser(selection);
                int resp = JOptionPane.showConfirmDialog(null, "¿Está seguro de que quiere eliminar al usuario: "+usuario.getDisplayName()+" ?", "Alerta!", JOptionPane.YES_NO_OPTION);
                if(resp == 0){
                    usuariosFrame.showLoadingDialog();
                    usuariosModel.deleteUser(usuario.getUid());
                }
            }
        }
    }

    private class UserListSelectionListener implements ListSelectionListener {
        public void valueChanged(ListSelectionEvent e) {
            if(e.getValueIsAdjusting()) {
                int selection = listUsuarios.getSelectedIndex();
                UserListModel userList = usuariosFrame.getUserListModel();

                UserInfo userInfo = userList.getUser(selection);
                usuariosFrame.setTxtUid(userInfo.getUid());
                usuariosFrame.setTxtDisplayName(isEmpty(userInfo.getDisplayName()) ? "SIN NOMBRE" : userInfo.getDisplayName());
                usuariosFrame.setTxtEmail(userInfo.getEmail());
                usuariosFrame.setTxtPassword("******");
            }
        }
    }

    private class ButtonLimpiarSeleccionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            listUsuarios.clearSelection();
            usuariosFrame.clearAll();
        }
    }

    private void actualizarListaUsuarios() {
        usuariosFrame.showLoadingDialog();
        usuariosModel.listAllUsers();
    }

    @Subscribe
    public void onListAllUserEvent(ListAllUsersEvent userListEvent) {
        if (userListEvent.isSuccess()) {
            usuariosFrame.actualizarListaUsuarios(userListEvent.getResult());
        } else {
            Sentry.captureException(new Exception("Error al listar usuarios: " + userListEvent.getErrorMessage()));
            JOptionPane.showMessageDialog(usuariosFrame, "Error al listar usuarios: " + userListEvent.getErrorMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
        usuariosFrame.hideLoadingDialog();
    }

    @Subscribe
    public void onCreateUserEvent(CreateUserEvent createUserEvent) {
        if (createUserEvent.isSuccess()) {
            UserRecord user = createUserEvent.getResult();
            usuariosModel.saveUser(user.getUid(),user.getDisplayName(),user.getEmail());
        } else {
            Sentry.captureException(new Exception("Error al crear usuario: " + createUserEvent.getErrorMessage()));
            JOptionPane.showMessageDialog(usuariosFrame, "Error al crear usuario: " + createUserEvent.getErrorMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    @Subscribe
    public void onUpdateUserEvent(UpdateUserEvent updateUserEvent) {
        if (updateUserEvent.isSuccess()) {
            UserRecord user = updateUserEvent.getResult();
            usuariosModel.saveUpdatedUser(user.getUid(),user.getDisplayName(),user.getEmail());
        } else {
            Sentry.captureException(new Exception("Error al actualizar usuario: " + updateUserEvent.getErrorMessage()));
            JOptionPane.showMessageDialog(usuariosFrame, "Error al actualizar usuario: " + updateUserEvent.getErrorMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    @Subscribe
    public void onSaveUserEvent(SaveUserEvent saveUserEvent) {
        if (saveUserEvent.isSuccess()) {
            if (saveUserEvent.getResult() != null) {
                JOptionPane.showMessageDialog(null, "El usuario se ha creado con exito", "Exito!", JOptionPane.INFORMATION_MESSAGE);
            }
            actualizarListaUsuarios();
        } else {
            Sentry.captureException(new Exception("Error al guardar usuario: " + saveUserEvent.getErrorMessage()));
            JOptionPane.showMessageDialog(usuariosFrame, "Error al guardar usuario: " + saveUserEvent.getErrorMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
        usuariosFrame.clearAll();
        usuariosFrame.hideLoadingDialog();
    }

    @Subscribe
    public void onSaveUpdatedUserEvent(SaveUpdatedUserEvent saveUpdatedUserEvent) {
        if (saveUpdatedUserEvent.isSuccess()) {
            if (saveUpdatedUserEvent.getResult() != null) {
                JOptionPane.showMessageDialog(null, "El usuario se ha actualizado con exito", "Exito!", JOptionPane.INFORMATION_MESSAGE);
            }
            actualizarListaUsuarios();
        } else {
            Sentry.captureException(new Exception("Error al guardar usuario actualizado: " + saveUpdatedUserEvent.getErrorMessage()));
            JOptionPane.showMessageDialog(usuariosFrame, "Error al guardar usuario actualizado: " + saveUpdatedUserEvent.getErrorMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
        usuariosFrame.clearAll();
        usuariosFrame.hideLoadingDialog();
    }

    @Subscribe
    public void onDeleteUserEvent(DeleteUserEvent deleteUserEvent) {
        if (deleteUserEvent.isSuccess()) {
            usuariosModel.deleteUserDB(deleteUserEvent.getResult());
        } else {
            Sentry.captureException(new Exception("Error al eliminar usuario: " + deleteUserEvent.getErrorMessage()));
            JOptionPane.showMessageDialog(usuariosFrame, "Error al eliminar usuario: " + deleteUserEvent.getErrorMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    @Subscribe
    public void onDeleteUserDBEvent(DeleteUserDBEvent deleteUserDBEvent) {
        if (deleteUserDBEvent.isSuccess()) {
            if (deleteUserDBEvent.getResult() != null) {
                JOptionPane.showMessageDialog(null, "El usuario " + deleteUserDBEvent.getResult() + " se ha eliminado con exito", "Exito!", JOptionPane.INFORMATION_MESSAGE);
            }
            actualizarListaUsuarios();
        } else {
            Sentry.captureException(new Exception("Error al eliminar usuario de la DB: " + deleteUserDBEvent.getErrorMessage()));
            JOptionPane.showMessageDialog(usuariosFrame, "Error al eliminar usuario de la base de datos: " + deleteUserDBEvent.getErrorMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
        usuariosFrame.clearAll();
        usuariosFrame.hideLoadingDialog();
    }
}
