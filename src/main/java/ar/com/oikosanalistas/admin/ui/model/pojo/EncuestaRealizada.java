package ar.com.oikosanalistas.admin.ui.model.pojo;

import ar.com.oikosanalistas.admin.utils.EncodeUtils;
import ar.com.oikosanalistas.admin.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

import static ar.com.oikosanalistas.admin.utils.StringUtils.isEmpty;
import static ar.com.oikosanalistas.admin.utils.Constants.EMPTY_FIELD;

/**
 * Created by jiurchuk on 3/5/18.
 */
public class EncuestaRealizada {

    private String id;
    private String idEncuesta;
    private String nombreEncuesta;
    private String idEncuestador;
    private String nombreEncuestador;
    private Map<String, String> mapPreguntaRespuesta;
    private String horaInicio;
    private String horaFin;
    private OikosLocation oikosLocation;

    public EncuestaRealizada() {
    }

    public EncuestaRealizada(String idEncuesta, String idEncuestador) {
        this.idEncuesta = idEncuesta;
        this.idEncuestador = idEncuestador;
        id = String.valueOf(System.currentTimeMillis());
    }

    public void addPreguntaRespuesta(String pregunta, String respuesta) {
        if (mapPreguntaRespuesta == null) {
            mapPreguntaRespuesta = new HashMap<>();
        }

        mapPreguntaRespuesta.put(pregunta, respuesta);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdEncuesta() {
        return idEncuesta;
    }

    public void setIdEncuesta(String idEncuesta) {
        this.idEncuesta = idEncuesta;
    }

    public String getIdEncuestador() {
        return idEncuestador;
    }

    public void setIdEncuestador(String idEncuestador) {
        this.idEncuestador = idEncuestador;
    }

    public Map<String, String> getMapPreguntaRespuesta() {
        return mapPreguntaRespuesta;
    }

    public void setMapPreguntaRespuesta(Map<String, String> mapPreguntaRespuesta) {
        this.mapPreguntaRespuesta = mapPreguntaRespuesta;
    }

    public String getNombreEncuesta() {
        return nombreEncuesta;
    }

    public void setNombreEncuesta(String nombreEncuesta) {
        this.nombreEncuesta = nombreEncuesta;
    }

    public String getNombreEncuestador() {
        return nombreEncuestador;
    }

    public void setNombreEncuestador(String nombreEncuestador) {
        this.nombreEncuestador = nombreEncuestador;
    }

    public String getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(String horaInicio) {
        this.horaInicio = horaInicio;
    }

    public String getHoraFin() {
        return horaFin;
    }

    public void setHoraFin(String horaFin) {
        this.horaFin = horaFin;
    }

    public String getHoraInicioAsString() {
        if (isEmpty(horaInicio)) {
            return EMPTY_FIELD;
        } else {
            return StringUtils.formatHour(horaInicio);
        }
    }

    public String getHoraFinAsString() {
        if (isEmpty(horaFin)) {
            return EMPTY_FIELD;
        } else {
            return StringUtils.formatHour(horaFin);
        }
    }

    public void setOikosLocation(OikosLocation oikosLocation) {
        this.oikosLocation = oikosLocation;
    }

    public OikosLocation getOikosLocation() {
        return oikosLocation;
    }

    public String getGoogleMapsLink() {
        return oikosLocation == null ? EMPTY_FIELD : oikosLocation.getGoogleMapsLink();
    }

    /**
     * Como en firebase estamos guardando las encuestas realizadas encodeadas, acá decodeamos el mapeo
     * de preguntas y respuestas para que puedan.
     *
     * NOTE: Esto es solo para poder generar el excel.
     * WARNING: MODIFICA EL MAPEO.
     */
    public void decodeAndReplaceMapPreguntaRespuesta() {
        Map<String, String> mapPreguntaRespuestaDecoded = new HashMap<>();

        for(Map.Entry<String, String> entry : mapPreguntaRespuesta.entrySet()) {
            mapPreguntaRespuestaDecoded.put(EncodeUtils.decode(entry.getKey()), entry.getValue());
        }

        mapPreguntaRespuesta = mapPreguntaRespuestaDecoded;
    }
}
