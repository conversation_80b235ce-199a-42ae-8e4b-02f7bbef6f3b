package ar.com.oikosanalistas.admin.ui.controller;

import ar.com.oikosanalistas.admin.events.ListAllEncuestasEvent;
import ar.com.oikosanalistas.admin.ui.model.SeleccionarEncuestasModel;
import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import ar.com.oikosanalistas.admin.ui.view.SeleccionarEncuestasFrame;
import org.greenrobot.eventbus.Subscribe;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.BorderLayout;
import javax.swing.border.EmptyBorder;

public class SeleccionarEncuestasController extends BaseController {

    private SeleccionarEncuestasFrame seleccionarEncuestasFrame;
    private SeleccionarEncuestasModel seleccionarEncuestasModel;

    private JList listaEncuestas;
    private JButton btnCancelar;
    private JButton btnSeleccionar;

    public SeleccionarEncuestasController() {
        seleccionarEncuestasFrame = new SeleccionarEncuestasFrame();
        seleccionarEncuestasModel = new SeleccionarEncuestasModel();

        initComponents();
        initListeners();
    }

    private void initComponents() {
        btnCancelar = seleccionarEncuestasFrame.getCancelarEncuestaBtn();
        btnSeleccionar = seleccionarEncuestasFrame.getSeleccionarEncuestaBtn();
        listaEncuestas = seleccionarEncuestasFrame.getListaEncuestas();
    }

    private void initListeners() {
        btnCancelar.addActionListener(new ButtonCancelarListener());
        btnSeleccionar.addActionListener(new ButtonSeleccionarListener());
    }

    public void showSeleccionarEncuestasFrameWindow() {
        seleccionarEncuestasFrame.setVisible(true);
        seleccionarEncuestasFrame.showLoadingDialog();
        // Cargar datos en un hilo separado para no bloquear el EDT
        new Thread(() -> {
            seleccionarEncuestasModel.listAllEncuestas();
        }).start();
    }

    @Subscribe
    public void onListAllEncuestasEvent(ListAllEncuestasEvent userListEvent) {
        SwingUtilities.invokeLater(() -> {
            if (userListEvent.isSuccess()) {
                seleccionarEncuestasModel.setEncuestasDescargadas(userListEvent.getResult());
                seleccionarEncuestasFrame.actualizarListaEncuestas(userListEvent.getResult());
            } else {
                seleccionarEncuestasFrame.showMessageDialog("Error al cargar encuestas: " + userListEvent.getErrorMessage());
            }
            seleccionarEncuestasFrame.hideLoadingDialog();
        });
    }

    private class ButtonSeleccionarListener implements ActionListener {

        @Override
        public void actionPerformed(ActionEvent e) {
            int position = listaEncuestas.getSelectedIndex();
            if (position < 0) {
                seleccionarEncuestasFrame.showMessageDialog("Selección inválida");
                return;
            }

            Encuesta encuestaSeleccionada = seleccionarEncuestasModel.getEncuestaEnPosition(position);
            seleccionarEncuestasFrame.dispose();
            EditarEncuestasController editarEncuestasController = new EditarEncuestasController(encuestaSeleccionada);
            editarEncuestasController.showFrameWindow();
        }
    }

    private class ButtonCancelarListener implements ActionListener {

        @Override
        public void actionPerformed(ActionEvent e) {
            if (seleccionarEncuestasFrame != null) {
                seleccionarEncuestasFrame.dispose();
            }
        }
    }
}
