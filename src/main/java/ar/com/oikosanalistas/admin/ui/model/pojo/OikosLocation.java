package ar.com.oikosanalistas.admin.ui.model.pojo;

import static ar.com.oikosanalistas.admin.utils.Constants.*;

/**
 * <AUTHOR>
 */

public class OikosLocation {

    private long time;
    private String latitude;
    private String longitude;
    private String altitude;

    public OikosLocation() {
    }

    public OikosLocation(long time, double latitude, double longitude, double altitude) {
        this.time = time;
        this.latitude = String.valueOf(latitude);
        this.longitude = String.valueOf(longitude);
        this.altitude = String.valueOf(altitude);
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getAltitude() {
        return altitude;
    }

    public void setAltitude(String altitude) {
        this.altitude = altitude;
    }

    public String getGoogleMapsLink() {
        return GOOGLE_MAPS_LINK + latitude + "," + longitude;
    }
}