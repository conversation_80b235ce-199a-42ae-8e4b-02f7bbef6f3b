package ar.com.oikosanalistas.admin.ui.view;

import ar.com.oikosanalistas.admin.ui.model.EncuestasListModel;
import ar.com.oikosanalistas.admin.ui.model.UserEncuestaListModel;
import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import ar.com.oikosanalistas.admin.ui.model.pojo.Usuario;
import ar.com.oikosanalistas.admin.ui.view.renderer.ZebraStripeCellRenderer;
import ar.com.oikosanalistas.admin.utils.Pair;

import javax.swing.*;
import java.awt.*;
import java.util.List;

public class AsignarEncuestasFrame extends BaseFrame {

    private static final int WIDTH = 500;
    private static final int HEIGHT = 500;

    private JList listEncuestas;
    private JPanel panelEncuestas;
    private JLabel lblTitulo;
    private JLabel lblDescripcion;
    private JTextField txtDisplayTitulo;
    private JTextField txtDisplayDescripcion;
    private JLabel lblAddUsuario;
    private JLabel lblDeleteUsuario;
    private JButton btnAddUsuario;
    private JButton btnDeleteUsuario;
    private JList listAddUsuario;
    private JList listDeleteUsuario;
    private EncuestasListModel encuestasListModel;
    private UserEncuestaListModel usuariosListModelAdd;
    private UserEncuestaListModel usuariosListModelDelete;

    public EncuestasListModel getEncuestasListModel() {
        return encuestasListModel;
    }

    public AsignarEncuestasFrame() {
        setupUI();
        setSize(800, 600);
        setMinimumSize(new Dimension(600, 400));
        setContentPane(panelEncuestas);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(WindowConstants.DISPOSE_ON_CLOSE);
        setTitle("Asignar Encuestas");
    }

    private void setupUI() {
        panelEncuestas = new JPanel(new BorderLayout(10, 10));
        panelEncuestas.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        panelEncuestas.add(createSurveySelectionPanel(), BorderLayout.NORTH);
        panelEncuestas.add(createUserAssignmentPanel(), BorderLayout.CENTER);

        listAddUsuario.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                btnAddUsuario.setEnabled(listAddUsuario.getSelectedIndex() != -1);
            }
        });
        listDeleteUsuario.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                btnDeleteUsuario.setEnabled(listDeleteUsuario.getSelectedIndex() != -1);
            }
        });
    }

    private JPanel createSurveySelectionPanel() {
        JPanel topPanel = new JPanel(new BorderLayout(10, 10));
        topPanel.setBorder(BorderFactory.createTitledBorder("1. Seleccione una Encuesta"));

        JPanel infoPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(2, 2, 2, 2);
        gbc.anchor = GridBagConstraints.WEST;

        lblTitulo = new JLabel("Título:");
        gbc.gridx = 0;
        gbc.gridy = 0;
        infoPanel.add(lblTitulo, gbc);

        txtDisplayTitulo = new JTextField();
        txtDisplayTitulo.setEditable(false);
        gbc.gridx = 1;
        gbc.weightx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        infoPanel.add(txtDisplayTitulo, gbc);

        lblDescripcion = new JLabel("Descripción:");
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.weightx = 0;
        gbc.fill = GridBagConstraints.NONE;
        infoPanel.add(lblDescripcion, gbc);

        txtDisplayDescripcion = new JTextField();
        txtDisplayDescripcion.setEditable(false);
        gbc.gridx = 1;
        gbc.gridy = 1;
        gbc.weightx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        infoPanel.add(txtDisplayDescripcion, gbc);
        
        topPanel.add(infoPanel, BorderLayout.NORTH);

        listEncuestas = new JList();
        listEncuestas.setFont(new Font("Dialog", Font.PLAIN, 16));
        listEncuestas.setCellRenderer(new ZebraStripeCellRenderer());
        JScrollPane scrollPane = new JScrollPane(listEncuestas);
        scrollPane.setPreferredSize(new Dimension(250, 150));
        topPanel.add(scrollPane, BorderLayout.CENTER);

        return topPanel;
    }

    private JPanel createUserAssignmentPanel() {
        JPanel centerPanel = new JPanel(new GridBagLayout());
        centerPanel.setBorder(BorderFactory.createTitledBorder("2. Asigne Usuarios"));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.BOTH;

        JPanel availablePanel = new JPanel(new BorderLayout(5,5));
        availablePanel.setBorder(BorderFactory.createTitledBorder("Disponibles"));
        listAddUsuario = new JList();
        listAddUsuario.setFont(new Font("Dialog", Font.PLAIN, 16));
        listAddUsuario.setCellRenderer(new ZebraStripeCellRenderer());
        availablePanel.add(new JScrollPane(listAddUsuario), BorderLayout.CENTER);
        
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.weightx = 1;
        gbc.weighty = 1;
        centerPanel.add(availablePanel, gbc);

        JPanel actionPanel = new JPanel(new GridBagLayout());
        GridBagConstraints actionGbc = new GridBagConstraints();
        actionGbc.insets = new Insets(5, 0, 5, 0);
        actionGbc.fill = GridBagConstraints.HORIZONTAL;
        
        btnAddUsuario = new JButton(">");
        btnAddUsuario.setToolTipText("Asignar usuario a encuesta");
        btnAddUsuario.setEnabled(false);
        actionGbc.gridx = 0;
        actionGbc.gridy = 0;
        actionPanel.add(btnAddUsuario, actionGbc);

        btnDeleteUsuario = new JButton("<");
        btnDeleteUsuario.setToolTipText("Quitar usuario de encuesta");
        btnDeleteUsuario.setEnabled(false);
        actionGbc.gridy = 1;
        actionPanel.add(btnDeleteUsuario, actionGbc);

        gbc.gridx = 1;
        gbc.weightx = 0;
        centerPanel.add(actionPanel, gbc);
        
        JPanel assignedPanel = new JPanel(new BorderLayout(5,5));
        assignedPanel.setBorder(BorderFactory.createTitledBorder("Asignados"));
        listDeleteUsuario = new JList();
        listDeleteUsuario.setFont(new Font("Dialog", Font.PLAIN, 16));
        listDeleteUsuario.setCellRenderer(new ZebraStripeCellRenderer());
        assignedPanel.add(new JScrollPane(listDeleteUsuario), BorderLayout.CENTER);

        gbc.gridx = 2;
        gbc.weightx = 1;
        centerPanel.add(assignedPanel, gbc);
        
        return centerPanel;
    }
    
    private void $$$setupUI$$$() {
        // Now managed by setupUI
    }

    @Override
    JPanel getPanel() {
        return panelEncuestas;
    }

    public JList getListEncuestas() {
        return listEncuestas;
    }

    private void createUIComponents() {
        // TODO: place custom component creation code here
    }

    public void setTxtDisplayTitulo(String txtTitulo){ this.txtDisplayTitulo.setText(txtTitulo); }

    public void setTxtDisplayDescripcion(String txtDescripcion){
        this.txtDisplayDescripcion.setText(txtDescripcion);
    }

    public void actualizarListaEncuestas(List<Encuesta> encuestasList) {
        encuestasListModel = new EncuestasListModel();
        for (Encuesta encuesta : encuestasList) {
            encuestasListModel.addEncuesta(encuesta);
        }
        listEncuestas.setModel(encuestasListModel);
    }

    public void actualizarListaUsuarios(Pair<List<Usuario>,String> resultado) {
        usuariosListModelAdd = new UserEncuestaListModel();
        usuariosListModelDelete = new UserEncuestaListModel();
        for (Usuario usuario : resultado.getKey()){
            if(usuario.getEncuestasAsignadas().contains(resultado.getValue())){
                usuariosListModelDelete.addUser(usuario);
            }
            else {
                usuariosListModelAdd.addUser(usuario);
            }

        }
        listAddUsuario.setModel(usuariosListModelAdd);
        listDeleteUsuario.setModel(usuariosListModelDelete);
    }

    public JButton getBtnAddUsuario() {
        return btnAddUsuario;
    }

    public JButton getBtnDeleteUsuario() {
        return btnDeleteUsuario;
    }

    public JList getListAddUsuario() {
        return listAddUsuario;
    }

    public JList getListDeleteUsuario() {
        return listDeleteUsuario;
    }

    public void setEnablebtnAddUsuario(){
        btnAddUsuario.setEnabled(true);
    }

    public void setEnablebtnDeleteUsuario(){
        btnDeleteUsuario.setEnabled(true);
    }

    public UserEncuestaListModel getUsuariosListModelAdd() {
        return usuariosListModelAdd;
    }

    public UserEncuestaListModel getUsuariosListModelDelete() {
        return usuariosListModelDelete;
    }
}
