package ar.com.oikosanalistas.admin.ui.model;

import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;

import javax.swing.*;
import java.util.ArrayList;
import java.util.List;

public class EncuestasListModel extends AbstractListModel {

    private List<Encuesta> encuestasList = new ArrayList<>();

    @Override
    public int getSize() {
        return encuestasList.size();
    }

    /**
     * Devuelve el elemento de la lista en la posición index
     *
     * @param index posición actual
     * @return objeto en la posición recibida por parámetro
     */
    @Override
    public Object getElementAt(int index) {
        Encuesta encuesta = encuestasList.get(index);
        return encuesta.getTitulo();
    }

    public void addEncuesta(Encuesta encuesta) {
        encuestasList.add(encuesta);
        this.fireIntervalAdded(this, getSize(), getSize() + 1);
    }

    public void eliminarEncuesta(int index) {
        encuestasList.remove(index);
        this.fireIntervalRemoved(index, getSize(), getSize() + 1);
    }

    public Encuesta getEncuestaAt(int index) {
        return encuestasList.get(index);
    }
}
