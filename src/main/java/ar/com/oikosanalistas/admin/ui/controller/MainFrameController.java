package ar.com.oikosanalistas.admin.ui.controller;

import ar.com.oikosanalistas.admin.events.CheckVersionEvent;
import ar.com.oikosanalistas.admin.ui.model.MainFrameModel;
import ar.com.oikosanalistas.admin.ui.model.pojo.LastVersion;
import ar.com.oikosanalistas.admin.ui.view.MainFrame;
import ar.com.oikosanalistas.admin.utils.Constants;
import org.greenrobot.eventbus.Subscribe;
import ar.com.oikosanalistas.admin.MainLauncher;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Paths;

public class MainFrameController extends BaseController {

    private MainFrame mainFrame;
    private MainFrameModel mainFrameModel;
    private JButton btnShowUsers;
    private JButton btnGenerarResultados;
    private JButton btnCrearEncuesta;
    private JButton btnAsignarEncuestas;
    private JButton btnVerEncuestas;
    private JButton btnEditarEncuestas;
    private JButton btnCheckVersion;
    private UsuariosController usuariosController;
    private GenerarResultadosController resultadosController;
    private CrearEncuestasController crearEncuestasController;
    private AsignarEncuestasController asignarEncuestasController;
    private VerEncuestasController verEncuestasController;
    private SeleccionarEncuestasController seleccionarEncuestasController;

    public MainFrameController() {
        initComponents();
        initListeners();
    }

    private void initComponents() {
        mainFrame = new MainFrame();
        mainFrameModel = new MainFrameModel();
        btnShowUsers = mainFrame.getBtnShowUsers();
        btnGenerarResultados = mainFrame.getBtnGenerarResultados();
        btnCrearEncuesta = mainFrame.getBtnCrearEncuesta();
        btnAsignarEncuestas = mainFrame.getBtnAsignarEncuestas();
        btnVerEncuestas = mainFrame.getBtnVerEncuestas();
        btnEditarEncuestas = mainFrame.getBtnEditarEncuestas();
        btnCheckVersion = mainFrame.getBtnCheckVersion();
    }

    private void initListeners() {
        btnShowUsers.addActionListener(new ButtonShowUsersListener());
        btnCrearEncuesta.addActionListener(new ButtonCrearEncuestaListener());
        btnGenerarResultados.addActionListener(new ButtonGenerarResultadosListener());
        btnAsignarEncuestas.addActionListener(new ButtonAsignarEncuestasListener());
        btnVerEncuestas.addActionListener(new ButtonVerEncuestasListener());
        btnEditarEncuestas.addActionListener(new ButtonEditarEncuestasListener());
        btnCheckVersion.addActionListener(new ButtonCheckVersionListener());
    }

    public void showMainFrameWindow() {
        mainFrame.setVisible(true);
    }


    private class ButtonCheckVersionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent actionEventEvent) {
            System.out.println("Iniciando verificación de versión...");
            mainFrame.showLoadingDialog();
            mainFrameModel.checkVersion();
        }
    }

    @Subscribe
    public void onCheckVersion(CheckVersionEvent checkVersionEvent) {
        System.out.println("Evento de verificación recibido, ocultando diálogo de carga...");
        mainFrame.hideLoadingDialog();

        if (checkVersionEvent.isSuccess()) {
            LastVersion version = checkVersionEvent.getResult();
            String serverVersion = (version != null) ? version.getAdmin() : null;
            String localVersion = Constants.OIKOS_VERSION;

            System.out.println("Versión recibida: " + serverVersion);
            System.out.println("Versión actual: " + localVersion);

            if (serverVersion != null) {
                try {
                    if (isServerVersionNewer(serverVersion, localVersion)) {
                        String[] buttons = {"Descargar", "Cancelar"};

                        int rc = JOptionPane.showOptionDialog(
                                mainFrame,
                                "Actualmente estás usando la versión " + localVersion + "\nEstá disponible la versión " + serverVersion,
                                "Nueva versión disponible",
                                JOptionPane.YES_NO_OPTION,
                                JOptionPane.INFORMATION_MESSAGE,
                                null,
                                buttons,
                                buttons[0]
                        );

                        if (rc == 0) {
                            downloadAndApplyUpdate(version.getAdmin_link());
                        }
                    } else {
                        JOptionPane.showMessageDialog(mainFrame, "Tu versión está actualizada.", "Check Version", JOptionPane.INFORMATION_MESSAGE);
                    }
                } catch (NumberFormatException e) {
                    JOptionPane.showMessageDialog(mainFrame, "Formato de versión inválido. No se puede comparar.\nServidor: " + serverVersion + ", Local: " + localVersion, "Error", JOptionPane.ERROR_MESSAGE);
                }

            } else {
                JOptionPane.showMessageDialog(mainFrame, "No se pudo obtener información de versión.", "Error", JOptionPane.ERROR_MESSAGE);
            }
        } else {
            System.out.println("Error en verificación: " + checkVersionEvent.getErrorMessage());
            JOptionPane.showMessageDialog(mainFrame, "No se pudo comprobar la versión: " + checkVersionEvent.getErrorMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    private boolean isServerVersionNewer(String serverVersion, String localVersion) {
        if (serverVersion == null || localVersion == null || serverVersion.isEmpty() || localVersion.isEmpty()) {
            return false;
        }
        String[] serverParts = serverVersion.split("\\.");
        String[] localParts = localVersion.split("\\.");
        int length = Math.max(serverParts.length, localParts.length);
        for (int i = 0; i < length; i++) {
            // Use trim() to avoid issues with spaces and parse each part as an integer
            int serverPart = i < serverParts.length ? Integer.parseInt(serverParts[i].trim()) : 0;
            int localPart = i < localParts.length ? Integer.parseInt(localParts[i].trim()) : 0;
            if (serverPart > localPart) {
                return true;
            }
            if (serverPart < localPart) {
                return false;
            }
        }
        // If all parts are equal, the versions are the same
        return false;
    }

    private void downloadAndApplyUpdate(String urlString) {
        JDialog downloadDialog = new JDialog(mainFrame, "Descargando Actualización", true);
        JProgressBar progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);
        downloadDialog.add(BorderLayout.CENTER, progressBar);
        downloadDialog.setSize(300, 75);
        downloadDialog.setLocationRelativeTo(mainFrame);

        SwingWorker<File, Integer> worker = new SwingWorker<File, Integer>() {
            @Override
            protected File doInBackground() throws Exception {
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setInstanceFollowRedirects(true);
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36");

                int responseCode = connection.getResponseCode();
                if (responseCode != HttpURLConnection.HTTP_OK) {
                    throw new IOException("El servidor respondió con el código: " + responseCode);
                }

                int totalSize = connection.getContentLength();
                if (totalSize == -1) {
                    progressBar.setIndeterminate(true);
                } else {
                    progressBar.setMaximum(totalSize);
                }

                File currentJarFile = getJarPath();
                File newJarFile = new File(currentJarFile.getParent(), "oikos.admin-nuevo.jar");

                try (InputStream in = connection.getInputStream();
                     FileOutputStream out = new FileOutputStream(newJarFile)) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    int totalBytesRead = 0;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                        totalBytesRead += bytesRead;
                        publish(totalBytesRead);
                    }
                }
                return newJarFile;
            }

            @Override
            protected void process(java.util.List<Integer> chunks) {
                for (int value : chunks) {
                    progressBar.setValue(value);
                }
            }

            @Override
            protected void done() {
                downloadDialog.setVisible(false);
                try {
                    File newJarFile = get();
                    JOptionPane.showMessageDialog(mainFrame, "Descarga completa. La aplicación se reiniciará para actualizar.");
                    runUpdaterProcess(getJarPath().getAbsolutePath(), newJarFile.getAbsolutePath());
                    System.exit(0);
                } catch (Exception e) {
                    e.printStackTrace();
                    JOptionPane.showMessageDialog(mainFrame, "Error al aplicar la actualización: " + e.getMessage(), "Error", JOptionPane.ERROR_MESSAGE);
                }
            }
        };

        worker.execute();
        downloadDialog.setVisible(true);
    }

    private void runUpdaterProcess(String currentJarPath, String newJarPath) throws IOException {
        String javaHome = System.getProperty("java.home");
        String javaBin = Paths.get(javaHome, "bin", "java").toString();
        String updaterClassName = "ar.com.oikosanalistas.admin.Updater";

        // Use the current JAR as the classpath, no need to extract the updater class.
        ProcessBuilder builder = new ProcessBuilder(
                javaBin,
                "-cp",
                currentJarPath,
                updaterClassName,
                currentJarPath,
                newJarPath
        );

        // Redirect the updater process's output/error stream to a log file for debugging.
        File logFile = new File("updater-process-output.log");
        builder.redirectErrorStream(true);
        builder.redirectOutput(logFile);

        builder.start();
    }

    private File getJarPath() throws URISyntaxException {
        return new File(MainLauncher.class.getProtectionDomain().getCodeSource().getLocation().toURI());
    }

    private void launchURL(String url) {
        if (Desktop.isDesktopSupported()) {
            try {
                Desktop.getDesktop().browse(new URI(url));
            } catch (IOException | URISyntaxException e) {
                e.printStackTrace();
            }
        }
    }

    private class ButtonShowUsersListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent actionEvent) {
            if (usuariosController != null) {
                usuariosController.showUsuariosFrameWindow();
            } else {
                usuariosController = new UsuariosController();
            }
            usuariosController.showUsuariosFrameWindow();
        }
    }

    private class ButtonCrearEncuestaListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent actionEvent) {
            if (crearEncuestasController != null) {
                crearEncuestasController.showFrameWindow();
            } else {
                crearEncuestasController = new CrearEncuestasController();
            }
            crearEncuestasController.showFrameWindow();
        }
    }

    private class ButtonGenerarResultadosListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent actionEvent) {
            if (resultadosController != null) {
                resultadosController.showResultadosFrameWindow();
            } else {
                resultadosController = new GenerarResultadosController();
            }
            resultadosController.showResultadosFrameWindow();
        }
    }

    private class ButtonAsignarEncuestasListener implements ActionListener {

        @Override
        public void actionPerformed(ActionEvent e) {
            if (asignarEncuestasController != null) {
                asignarEncuestasController.showEncuestasFrameWindow();
            } else {
                asignarEncuestasController = new AsignarEncuestasController();
            }
            asignarEncuestasController.showEncuestasFrameWindow();
        }
    }

    private class ButtonVerEncuestasListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (verEncuestasController != null) {
                verEncuestasController.showVerEncuestasFrameWindow();
            } else {
                verEncuestasController = new VerEncuestasController();
            }
            verEncuestasController.showVerEncuestasFrameWindow();
        }
    }

    private class ButtonEditarEncuestasListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (seleccionarEncuestasController == null) {
                seleccionarEncuestasController = new SeleccionarEncuestasController();
            }
            seleccionarEncuestasController.showSeleccionarEncuestasFrameWindow();
        }
    }
}
