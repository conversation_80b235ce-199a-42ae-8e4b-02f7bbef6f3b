package ar.com.oikosanalistas.admin.ui.view;

import ar.com.oikosanalistas.admin.ui.model.EncuestasListModel;
import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import ar.com.oikosanalistas.admin.ui.view.renderer.ZebraStripeCellRenderer;

import javax.swing.*;
import java.awt.*;
import java.util.List;

public class GenerarResultadosFrame extends BaseFrame {

    private static final int WIDTH = 500;
    private static final int HEIGHT = 500;
    private JPanel resultadosPanel;
    private JButton btnGenerar;
    private JList listEncuestas;
    private EncuestasListModel encuestasListModel;

    public GenerarResultadosFrame() {
        setSize(WIDTH, HEIGHT);
        setupUI();
        setContentPane(resultadosPanel);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(WindowConstants.DISPOSE_ON_CLOSE);
        setTitle("Generar Resultados");
    }

    private void setupUI() {
        resultadosPanel = new JPanel(new GridBagLayout());
        resultadosPanel.setBorder(BorderFactory.createEmptyBorder(16, 16, 16, 16));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(4, 4, 4, 4);

        // Survey list
        listEncuestas = new JList();
        listEncuestas.setFont(new Font(Font.DIALOG, Font.PLAIN, 16));
        listEncuestas.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        // Custom renderer for zebra striping
        listEncuestas.setCellRenderer(new ZebraStripeCellRenderer());

        JScrollPane scrollPane = new JScrollPane(listEncuestas);
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.weightx = 1;
        gbc.weighty = 1;
        gbc.fill = GridBagConstraints.BOTH;
        resultadosPanel.add(scrollPane, gbc);

        // Generate button
        btnGenerar = new JButton("Generar resultados para encuesta seleccionada");
        btnGenerar.setFont(new Font(Font.DIALOG, Font.PLAIN, 16));
        btnGenerar.setEnabled(false);
        gbc.gridy = 1;
        gbc.weighty = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        resultadosPanel.add(btnGenerar, gbc);

        // Add selection listener to the list
        listEncuestas.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                btnGenerar.setEnabled(listEncuestas.getSelectedIndex() != -1);
            }
        });
    }

    @Override
    JPanel getPanel() {
        return resultadosPanel;
    }

    public JButton getBtnGenerar() {
        return btnGenerar;
    }

    public JList getListEncuestas() {
        return listEncuestas;
    }

    public EncuestasListModel getEncuestasListModel() {
        return encuestasListModel;
    }

    public void actualizarListaEncuestas(List<Encuesta> encuestasList) {
        encuestasListModel = new EncuestasListModel();
        for (Encuesta encuesta : encuestasList) {
            encuestasListModel.addEncuesta(encuesta);
        }
        listEncuestas.setModel(encuestasListModel);
    }

    {
// GUI initializer generated by IntelliJ IDEA GUI Designer
// >>> IMPORTANT!! <<<
// DO NOT EDIT OR ADD ANY CODE HERE!
        $$$setupUI$$$();
    }

    /**
     * Method generated by IntelliJ IDEA GUI Designer
     * >>> IMPORTANT!! <<<
     * DO NOT edit this method OR call it in your code!
     *
     * @noinspection ALL
     */
    private void $$$setupUI$$$() {
        // Now managed by setupUI with GridBagLayout
    }

    /**
     * @noinspection ALL
     */
    public JComponent $$$getRootComponent$$$() {
        return resultadosPanel;
    }
}
