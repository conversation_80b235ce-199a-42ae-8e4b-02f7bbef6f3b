package ar.com.oikosanalistas.admin.ui.view;

import ar.com.oikosanalistas.admin.ui.model.EncuestasListModel;
import ar.com.oikosanalistas.admin.ui.model.PreguntasListModel;
import ar.com.oikosanalistas.admin.ui.model.RespuestasListModel;
import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import ar.com.oikosanalistas.admin.ui.model.pojo.pregunta.Pregunta;
import ar.com.oikosanalistas.admin.ui.model.pojo.respuesta.Respuesta;
import ar.com.oikosanalistas.admin.ui.view.renderer.ZebraStripeCellRenderer;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.util.List;

public class VerEncuestasFrame extends BaseFrame {

    private JList<Encuesta> listEncuestas;
    private JList<Pregunta> listPreguntas;
    private JList<Respuesta> listRespuestas;
    private JButton btnEliminarEncuesta;
    private JPanel mainPanel;
    private JButton btnConfigurarFiltros;
    private JButton btnGuardarEncuesta;
    private JButton btnClonarEncuesta;
    private EncuestasListModel encuestasListModel;
    private PreguntasListModel preguntasListModel;
    private RespuestasListModel respuestasListModel;


    public VerEncuestasFrame() {
        setTitle("Administración de Encuestas");
        setDefaultCloseOperation(WindowConstants.DISPOSE_ON_CLOSE);
        setSize(1024, 768);
        setLocationRelativeTo(null);

        mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(new EmptyBorder(10, 10, 10, 10));
        setContentPane(mainPanel);

        // --- Paneles de Listas ---
        listEncuestas = new JList<>();
        listPreguntas = new JList<>();
        listRespuestas = new JList<>();

        listPreguntas.setCellRenderer(new DefaultListCellRenderer() {
            @Override
            public Component getListCellRendererComponent(JList<?> list, Object value, int index, boolean isSelected, boolean cellHasFocus) {
                super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
                if (value instanceof Pregunta) {
                    Pregunta pregunta = (Pregunta) value;
                    setText(index + ": " + pregunta.getDescripcion() + " (" + pregunta.getTipo() + ")");
                }
                return this;
            }
        });

        listRespuestas.setCellRenderer(new DefaultListCellRenderer() {
            @Override
            public Component getListCellRendererComponent(JList<?> list, Object value, int index, boolean isSelected, boolean cellHasFocus) {
                super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
                if (value instanceof Respuesta) {
                    Respuesta respuesta = (Respuesta) value;
                    String toReturn = respuesta.getId() + ": " + respuesta.getDescripcion();
                    if (respuesta.getSiguientePregunta() != null && respuesta.getSiguientePregunta() >= 0) {
                        toReturn += " (-> Preg " + respuesta.getSiguientePregunta() + ")";
                    }
                    setText(toReturn);
                }
                return this;
            }
        });

        JScrollPane encuestasScrollPane = new JScrollPane(listEncuestas);
        encuestasScrollPane.setBorder(new TitledBorder("Encuestas"));

        JScrollPane preguntasScrollPane = new JScrollPane(listPreguntas);
        preguntasScrollPane.setBorder(new TitledBorder("Preguntas"));

        JScrollPane respuestasScrollPane = new JScrollPane(listRespuestas);
        respuestasScrollPane.setBorder(new TitledBorder("Respuestas"));

        // --- Panel Izquierdo (Encuestas y sus botones) ---
        JPanel leftPanel = new JPanel(new BorderLayout(5, 5));
        leftPanel.add(encuestasScrollPane, BorderLayout.CENTER);

        btnClonarEncuesta = new JButton("Clonar Encuesta");
        btnEliminarEncuesta = new JButton("Eliminar Encuesta");
        JPanel encuestasButtonPanel = new JPanel(new GridLayout(1, 2, 5, 0));
        encuestasButtonPanel.add(btnClonarEncuesta);
        encuestasButtonPanel.add(btnEliminarEncuesta);
        leftPanel.add(encuestasButtonPanel, BorderLayout.SOUTH);

        // --- Panel Derecho (Preguntas y Respuestas) ---
        JSplitPane rightSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT, preguntasScrollPane, respuestasScrollPane);
        rightSplitPane.setResizeWeight(0.5);

        // --- Split principal ---
        JSplitPane mainSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, leftPanel, rightSplitPane);
        mainSplitPane.setResizeWeight(0.35);
        mainPanel.add(mainSplitPane, BorderLayout.CENTER);


        // --- Panel de botones inferior ---
        btnGuardarEncuesta = new JButton("Guardar Encuesta");
        btnConfigurarFiltros = new JButton("Configurar Filtros");

        JPanel bottomButtonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 0));
        bottomButtonPanel.add(btnGuardarEncuesta);
        bottomButtonPanel.add(btnConfigurarFiltros);
        mainPanel.add(bottomButtonPanel, BorderLayout.SOUTH);

        listEncuestas.setCellRenderer(new ZebraStripeCellRenderer());
        listPreguntas.setCellRenderer(new ZebraStripeCellRenderer());
        listRespuestas.setCellRenderer(new ZebraStripeCellRenderer());
    }

    @Override
    JPanel getPanel() {
        return mainPanel;
    }

    public JList getListEncuestas() {
        return listEncuestas;
    }

    public JList getListPreguntas() {
        return listPreguntas;
    }

    public JList getListRespuestas() {
        return listRespuestas;
    }

    public JButton getBtnEliminarEncuesta() {
        return btnEliminarEncuesta;
    }

    public JButton getBtnClonarEncuesta() {
        return btnClonarEncuesta;
    }

    public EncuestasListModel getEncuestasListModel() {
        return encuestasListModel;
    }

    public PreguntasListModel getPreguntasListModel() {
        return preguntasListModel;
    }

    public RespuestasListModel getRespuestasListModel() {
        return respuestasListModel;
    }

    public JButton getBtnConfigurarFiltros() {
        return btnConfigurarFiltros;
    }

    public JButton getBtnGuardarEncuesta() {
        return btnGuardarEncuesta;
    }

    public void actualizarListaEncuestas(List<Encuesta> encuestasList) {
        encuestasListModel = new EncuestasListModel();
        for (Encuesta encuesta : encuestasList) {
            encuestasListModel.addEncuesta(encuesta);
        }
        listEncuestas.setModel(encuestasListModel);
    }

    public void actualizarListaPreguntas(List<Pregunta> preguntasList) {
        preguntasListModel = new PreguntasListModel();
        for (Pregunta pregunta : preguntasList) {
            preguntasListModel.addPregunta(pregunta);
        }
        listPreguntas.setModel(preguntasListModel);
    }

    public void actualizarListaRespuestas(List<Respuesta> respuestaList) {
        respuestasListModel = new RespuestasListModel();
        for (Respuesta respuesta : respuestaList) {
            respuestasListModel.addRespuesta(respuesta);
        }
        listRespuestas.setModel(respuestasListModel);
    }

    public void clearListPreguntas() {
        if (preguntasListModel != null) {
            preguntasListModel.clear();
        }
    }

    public void clearListRespuestas() {
        if (respuestasListModel != null) {
            respuestasListModel.clear();
        }
    }
}
