package ar.com.oikosanalistas.admin.ui.model.pojo;

import ar.com.oikosanalistas.admin.ui.model.pojo.pregunta.Pregunta;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public class Encuesta {

    private String id;
    private String titulo;
    private String descripcion;
    private String fechaInicio; //Valida desde
    private String fechaFin; //Valida hasta
    private String fechaRealizada; //Fecha y hora en que fue realizada la encuesta
    private List<String> encuestadoresAsignados;
    private List<Pregunta> preguntasList;

    public Encuesta() {
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    public String getFechaInicio() {
        return fechaInicio;
    }

    public void setFechaInicio(String fechaInicio) {
        this.fechaInicio = fechaInicio;
    }

    public String getFechaFin() {
        return fechaFin;
    }

    public void setFechaFin(String fechaFin) {
        this.fechaFin = fechaFin;
    }

    public String getFechaRealizada() {
        return fechaRealizada;
    }

    public void setFechaRealizada(String fechaRealizada) {
        this.fechaRealizada = fechaRealizada;
    }

    public List<String> getEncuestadoresAsignados() {
        if (encuestadoresAsignados == null)
            encuestadoresAsignados = new LinkedList<>();
        return encuestadoresAsignados;
    }

    public void setEncuestadoresAsignados(List<String> encuestadoresAsignados) {
        this.encuestadoresAsignados = encuestadoresAsignados;
    }

    public List<Pregunta> getPreguntasList() {
        return preguntasList;
    }

    public void setPreguntasList(List<Pregunta> preguntasList) {
        this.preguntasList = preguntasList;
    }

    public void agregarPregunta(Pregunta pregunta) {
        if (preguntasList == null || preguntasList.isEmpty()) {
            preguntasList = new ArrayList<>();
        }

        pregunta.setId(preguntasList.size());
        preguntasList.add(pregunta);
    }
}