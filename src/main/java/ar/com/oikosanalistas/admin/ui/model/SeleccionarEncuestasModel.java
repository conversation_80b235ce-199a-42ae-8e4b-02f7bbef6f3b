package ar.com.oikosanalistas.admin.ui.model;

import ar.com.oikosanalistas.admin.events.ListAllEncuestasEvent;
import ar.com.oikosanalistas.admin.ui.model.pojo.Encuesta;
import com.google.firebase.database.*;

import java.util.ArrayList;
import java.util.List;

import static ar.com.oikosanalistas.admin.utils.Constants.ENCUESTAS_PATH;

public class SeleccionarEncuestasModel extends BaseModel {

    private List<Encuesta> encuestaList;

    public void setEncuestasDescargadas(List<Encuesta> encuestaList) {
        this.encuestaList = encuestaList;
    }

    public void listAllEncuestas() {
        final FirebaseDatabase database = FirebaseDatabase.getInstance();
        DatabaseReference ref = database.getReference(ENCUESTAS_PATH);

        ref.addListenerForSingleValueEvent(new ValueEventListener() {
            @Override
            public void onDataChange(DataSnapshot dataSnapshot) {
                List<Encuesta> encuestaList = new ArrayList<>();
                for (DataSnapshot children : dataSnapshot.getChildren()) {
                    Encuesta encuesta = children.getValue(Encuesta.class);
                    encuesta.setId(children.getKey());
                    encuestaList.add(encuesta);
                }

                postBus(new ListAllEncuestasEvent(encuestaList));
            }

            @Override
            public void onCancelled(DatabaseError error) {
                System.out.println("Error obteniendo la lista de encuestas");
                postBus(new ListAllEncuestasEvent("Error obteniendo la lista de encuestas", error.toException()));
            }
        });
    }

    public Encuesta getEncuestaEnPosition(int position) {
        if (encuestaList != null && !encuestaList.isEmpty()) {
            return encuestaList.get(position);
        }
            return null;
    }
}
