package ar.com.oikosanalistas.admin.ui.model.pojo;

import java.util.ArrayList;
import java.util.List;

public class Usuario {

    private String id;
    private String displayName;
    private String email;
    private List<String> encuestasAsignadas;

    public Usuario() {
        this(null,null,null);
    }

    public Usuario(String id, String displayName, String email) {
        this.id = id;
        this.displayName = displayName;
        this.email = email;
        this.encuestasAsignadas = new ArrayList<>();
    }

    public String getId() { return id; }

    public String getDisplayName() {
        return displayName;
    }

    public String getEmail() {
        return email;
    }

    public List<String> getEncuestasAsignadas() {
        return encuestasAsignadas;
    }

    public void addEncuesta(String id){
        encuestasAsignadas.add(id);
    }

    public void removeEncuesta(String id){
        encuestasAsignadas.remove(id);
    }
}
