package ar.com.oikosanalistas.admin.ui.model;

import ar.com.oikosanalistas.admin.ui.model.pojo.respuesta.Respuesta;

import javax.swing.*;
import java.util.ArrayList;
import java.util.List;

public class RespuestasListModel extends AbstractListModel<Respuesta> {

    private List<Respuesta> respuestaList;

    public RespuestasListModel(List<Respuesta> respuestaList) {
        this.respuestaList = respuestaList;
    }

    public RespuestasListModel() {
        respuestaList = new ArrayList<>();
    }

    @Override
    public int getSize() {
        return respuestaList == null ? 0 : respuestaList.size();
    }

    @Override
    public Respuesta getElementAt(int index) {
        return respuestaList.get(index);
    }

    private boolean hasSiguientePregunta(int index) {
        return respuestaList.get(index).getSiguientePregunta() != null && respuestaList.get(index).getSiguientePregunta() >= 0;
    }

    private String getSiguientePregunta(int index) {
        return String.valueOf(respuestaList.get(index).getSiguientePregunta());
    }

    public void addRespuesta(Respuesta respuesta) {
        int index = respuestaList.size();
        respuestaList.add(respuesta);
        fireIntervalAdded(this, index, index);
    }

    public void clear() {
        int index = respuestaList.size() - 1;
        if (index < 0) {
            return;
        }
        respuestaList.clear();
        fireIntervalRemoved(this, 0, index);
    }

    public Respuesta getRespuestaAt(int index) {
        return respuestaList.get(index);
    }
}
