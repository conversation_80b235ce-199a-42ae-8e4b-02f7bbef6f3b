package ar.com.oikosanalistas.admin.ui.view;

import ar.com.oikosanalistas.admin.ui.view.viewmodel.NuevaPreguntaCategoricaViewModel;
import ar.com.oikosanalistas.admin.ui.view.viewmodel.NuevaPreguntaMixtaViewModel;
import ar.com.oikosanalistas.admin.ui.view.viewmodel.NuevaPreguntaMultipleViewModel;
import ar.com.oikosanalistas.admin.ui.view.viewmodel.NuevaPreguntaNumericaViewModel;
import ar.com.oikosanalistas.admin.ui.view.viewmodel.NuevaPreguntaTextoViewModel;
import ar.com.oikosanalistas.admin.ui.view.renderer.ZebraStripeCellRenderer;
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.text.NumberFormat;
import java.util.Collections;
import static ar.com.oikosanalistas.admin.utils.Constants.*;
import static ar.com.oikosanalistas.admin.utils.StringUtils.isEmpty;

public class CrearPreguntaDialog extends JDialog {

    private JComboBox<String> comboTipoPregunta;
    private JTextField txtDescripcion;
    private JPanel cardPanel;
    private CardLayout cardLayout;

    private JButton buttonOK;
    private JButton buttonCancel;
    
    // Panel para tipo Categorica/Multiple/Mixta
    private JTextField txtNuevaRespuesta;
    private JList<String> listRespuestas;
    private DefaultListModel<String> listModel;
    private JButton btnAgregarRespuesta;
    private JButton btnEditarRespuesta;
    private JButton btnEliminarRespuesta;
    
    // Panel para tipo Numerica
    private JFormattedTextField txtMin;
    private JFormattedTextField txtMax;

    // Listener
    private CrearPreguntaDialogListener listener;

    public interface CrearPreguntaDialogListener {
        void onOkPressedPreguntaCategorica(NuevaPreguntaCategoricaViewModel viewModel);
        void onOkPressedPreguntaNumerica(NuevaPreguntaNumericaViewModel viewModel);
        void onOkPressedPreguntaTexto(NuevaPreguntaTextoViewModel viewModel);
        void onOkPressedPreguntaMultiple(NuevaPreguntaMultipleViewModel viewModel);
        void onOkPressedPreguntaMixta(NuevaPreguntaMixtaViewModel viewModel);
        void onCancelPressed();
    }

    public CrearPreguntaDialog(String titulo, CrearPreguntaDialogListener listener) {
        this.listener = listener;
        setTitle(titulo);
        setModal(true);
        setupUI();
        initListeners();
        setMinimumSize(new Dimension(450, 400));
        pack();
        setLocationRelativeTo(null);
    }

    private void setupUI() {
        JPanel contentPane = new JPanel(new BorderLayout(10, 10));
        contentPane.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        setContentPane(contentPane);

        // Top Panel
        JPanel topPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;

        gbc.gridx = 0; gbc.gridy = 0;
        topPanel.add(new JLabel("Tipo de Pregunta:"), gbc);
        
        gbc.gridx = 1; gbc.gridy = 0;
        comboTipoPregunta = new JComboBox<>(TIPOS_PREGUNTA);
        topPanel.add(comboTipoPregunta, gbc);

        gbc.gridx = 0; gbc.gridy = 1;
        topPanel.add(new JLabel("Descripción:"), gbc);
        
        gbc.gridx = 1; gbc.gridy = 1; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        txtDescripcion = new JTextField();
        topPanel.add(txtDescripcion, gbc);
        
        contentPane.add(topPanel, BorderLayout.NORTH);

        // Center Panel (Dynamic)
        cardLayout = new CardLayout();
        cardPanel = new JPanel(cardLayout);

        cardPanel.add(createCategoricalPanel(), CATEGORICA);
        cardPanel.add(createNumericPanel(), NUMERICA);
        cardPanel.add(new JPanel(), TEXTO); // Empty panel for TEXTO

        contentPane.add(cardPanel, BorderLayout.CENTER);

        // Bottom Panel
        JPanel bottomPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonOK = new JButton("OK");
        buttonCancel = new JButton("Cancel");
        bottomPanel.add(buttonOK);
        bottomPanel.add(buttonCancel);
        contentPane.add(bottomPanel, BorderLayout.SOUTH);
    }
    
    private JPanel createCategoricalPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));

        // Add new answer panel
        JPanel addAnswerPanel = new JPanel(new BorderLayout(5, 5));
        addAnswerPanel.setBorder(BorderFactory.createTitledBorder("Nueva Respuesta"));
        txtNuevaRespuesta = new JTextField();
        btnAgregarRespuesta = new JButton("Agregar");
        addAnswerPanel.add(txtNuevaRespuesta, BorderLayout.CENTER);
        addAnswerPanel.add(btnAgregarRespuesta, BorderLayout.EAST);
        panel.add(addAnswerPanel, BorderLayout.NORTH);

        // Answers Panel
        JPanel answersPanel = new JPanel(new BorderLayout(10, 10));
        answersPanel.setBorder(BorderFactory.createTitledBorder("Respuestas Agregadas"));

        listModel = new DefaultListModel<>();
        listRespuestas = new JList<>(listModel);
        listRespuestas.setCellRenderer(new ZebraStripeCellRenderer());
        answersPanel.add(new JScrollPane(listRespuestas), BorderLayout.CENTER);

        JPanel answerButtonsPanel = new JPanel(new GridLayout(0, 1, 5, 5));
        btnEditarRespuesta = new JButton("Editar");
        btnEliminarRespuesta = new JButton("Eliminar");
        btnEditarRespuesta.setEnabled(false);
        btnEliminarRespuesta.setEnabled(false);
        answerButtonsPanel.add(btnEditarRespuesta);
        answerButtonsPanel.add(btnEliminarRespuesta);
        answersPanel.add(answerButtonsPanel, BorderLayout.EAST);

        panel.add(answersPanel, BorderLayout.CENTER);
        return panel;
    }
    
    private JPanel createNumericPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5,5,5,5);
        gbc.anchor = GridBagConstraints.WEST;

        NumberFormat integerFormat = NumberFormat.getIntegerInstance();
        integerFormat.setGroupingUsed(false);

        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("Valor Mínimo:"), gbc);
        txtMin = new JFormattedTextField(integerFormat);
        txtMin.setColumns(10);
        gbc.gridx = 1; gbc.gridy = 0;
        panel.add(txtMin, gbc);
        
        gbc.gridx = 0; gbc.gridy = 1;
        panel.add(new JLabel("Valor Máximo:"), gbc);
        txtMax = new JFormattedTextField(integerFormat);
        txtMax.setColumns(10);
        gbc.gridx = 1; gbc.gridy = 1;
        panel.add(txtMax, gbc);

        return panel;
    }

    private void initListeners() {
        comboTipoPregunta.addActionListener(e -> {
            String selected = (String) comboTipoPregunta.getSelectedItem();
            if (selected.equals(MULTIPLE) || selected.equals(MIXTA)) {
                cardLayout.show(cardPanel, CATEGORICA);
            } else {
                cardLayout.show(cardPanel, selected);
            }
        });

        buttonOK.addActionListener(e -> onOk());
        buttonCancel.addActionListener(e -> onCancel());

        // Listeners for Categorical panel
        Action addAction = new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                String nuevaRespuesta = txtNuevaRespuesta.getText();
                if (!isEmpty(nuevaRespuesta) && !listModel.contains(nuevaRespuesta)) {
                    listModel.addElement(nuevaRespuesta);
                    txtNuevaRespuesta.setText("");
                }
                txtNuevaRespuesta.requestFocus();
            }
        };
        btnAgregarRespuesta.addActionListener(addAction);
        txtNuevaRespuesta.addActionListener(addAction);

        listRespuestas.addListSelectionListener(e -> {
            boolean isSelected = listRespuestas.getSelectedIndex() != -1;
            btnEditarRespuesta.setEnabled(isSelected);
            btnEliminarRespuesta.setEnabled(isSelected);
        });

        btnEditarRespuesta.addActionListener(e -> {
            int selectedIndex = listRespuestas.getSelectedIndex();
            if (selectedIndex != -1) {
                String currentValue = listModel.getElementAt(selectedIndex);
                String newValue = JOptionPane.showInputDialog(this, "Editar respuesta:", currentValue);
                if (!isEmpty(newValue) && !listModel.contains(newValue)) {
                    listModel.setElementAt(newValue, selectedIndex);
                }
            }
        });

        btnEliminarRespuesta.addActionListener(e -> {
            int selectedIndex = listRespuestas.getSelectedIndex();
            if (selectedIndex != -1) {
                listModel.remove(selectedIndex);
            }
        });
    }

    private void onOk() {
        String selectedType = (String) comboTipoPregunta.getSelectedItem();
        String description = txtDescripcion.getText();

        if (isEmpty(description)) {
            JOptionPane.showMessageDialog(this, "La descripción es requerida.", "Error", JOptionPane.ERROR_MESSAGE);
            return;
        }

        switch (selectedType) {
            case CATEGORICA:
                NuevaPreguntaCategoricaViewModel catViewModel = new NuevaPreguntaCategoricaViewModel();
                catViewModel.setDescripcion(description);
                catViewModel.setRespuestas(Collections.list(listModel.elements()));
                if (catViewModel.getRespuestas().isEmpty()) {
                    JOptionPane.showMessageDialog(this, "Se requiere al menos una respuesta.", "Error", JOptionPane.ERROR_MESSAGE);
                    return;
                }
                listener.onOkPressedPreguntaCategorica(catViewModel);
                break;
            case MULTIPLE:
                NuevaPreguntaMultipleViewModel multViewModel = new NuevaPreguntaMultipleViewModel();
                multViewModel.setDescripcion(description);
                multViewModel.setRespuestas(Collections.list(listModel.elements()));
                if (multViewModel.getRespuestas().isEmpty()) {
                    JOptionPane.showMessageDialog(this, "Se requiere al menos una respuesta.", "Error", JOptionPane.ERROR_MESSAGE);
                    return;
                }
                listener.onOkPressedPreguntaMultiple(multViewModel);
                break;
            case MIXTA:
                 NuevaPreguntaMixtaViewModel mixtaViewModel = new NuevaPreguntaMixtaViewModel();
                mixtaViewModel.setDescripcion(description);
                mixtaViewModel.setRespuestas(Collections.list(listModel.elements()));
                if (mixtaViewModel.getRespuestas().isEmpty()) {
                    JOptionPane.showMessageDialog(this, "Se requiere al menos una respuesta.", "Error", JOptionPane.ERROR_MESSAGE);
                    return;
                }
                listener.onOkPressedPreguntaMixta(mixtaViewModel);
                break;
            case NUMERICA:
                try {
                    int min = Integer.parseInt(txtMin.getText());
                    int max = Integer.parseInt(txtMax.getText());
                    if (min >= max) {
                         JOptionPane.showMessageDialog(this, "El valor mínimo debe ser menor que el valor máximo.", "Error", JOptionPane.ERROR_MESSAGE);
                        return;
                    }
                    NuevaPreguntaNumericaViewModel numViewModel = new NuevaPreguntaNumericaViewModel();
                    numViewModel.setDescripcion(description);
                    numViewModel.setMinValue(min);
                    numViewModel.setMaxValue(max);
                    listener.onOkPressedPreguntaNumerica(numViewModel);
                } catch (NumberFormatException e) {
                     JOptionPane.showMessageDialog(this, "Los valores mínimo y máximo deben ser números enteros.", "Error", JOptionPane.ERROR_MESSAGE);
                    return;
                }
                break;
            case TEXTO:
                 NuevaPreguntaTextoViewModel textoViewModel = new NuevaPreguntaTextoViewModel();
                textoViewModel.setDescripcion(description);
                listener.onOkPressedPreguntaTexto(textoViewModel);
                break;
        }
        dispose();
    }

    private void onCancel() {
        listener.onCancelPressed();
        dispose();
    }
} 