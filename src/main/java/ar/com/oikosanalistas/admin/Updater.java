package ar.com.oikosanalistas.admin;

import java.io.File;
import java.io.IOException;
import java.io.PrintStream;
import java.net.URISyntaxException;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;

public class Updater {
    public static void main(String[] args) {
        String logFilePath = "updater-log.txt";
        try (PrintStream log = new PrintStream(logFilePath)) {
            log.println("Updater started at: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            if (args.length != 2) {
                log.println("Error: Invalid arguments. Expected 2, got " + args.length);
                System.err.println("Usage: java Updater <current-jar-path> <new-jar-path>");
                return;
            }

            String currentJarPath = args[0];
            String newJarPath = args[1];

            log.println("Current JAR: " + currentJarPath);
            log.println("New JAR: " + newJarPath);

            try {
                log.println("Waiting for main app to shut down...");
                Thread.sleep(3000); // Increased delay slightly for safety

                File currentJar = new File(currentJarPath);
                File newJar = new File(newJarPath);

                if (!newJar.exists()) {
                    log.println("Error: New JAR file not found at " + newJarPath);
                    return;
                }

                if (currentJar.exists()) {
                    log.println("Attempting to delete old JAR: " + currentJarPath);
                    if (!currentJar.delete()) {
                        log.println("Error: Could not delete old JAR. It might be in use or protected.");
                        // Attempt to rename it instead as a fallback
                        File oldJarBak = new File(currentJarPath + ".bak");
                        if (currentJar.renameTo(oldJarBak)){
                            log.println("Successfully renamed old JAR to .bak file. Will be deleted on next update.");
                        } else {
                            log.println("Error: Could not rename old JAR either. Update failed.");
                            return;
                        }
                    } else {
                        log.println("Old JAR deleted successfully.");
                    }
                }

                log.println("Attempting to rename new JAR to: " + currentJarPath);
                if (!newJar.renameTo(currentJar)) {
                    log.println("Error: Could not rename new JAR.");
                    return;
                }
                log.println("New JAR renamed successfully.");

                log.println("Relaunching application...");
                String javaHome = System.getProperty("java.home");
                String javaBin = Paths.get(javaHome, "bin", "java").toString();
                new ProcessBuilder(javaBin, "-jar", currentJarPath).start();
                log.println("Relaunch command sent.");

            } catch (InterruptedException | IOException e) {
                e.printStackTrace(log);
            }

            log.println("Updater finished at: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

        } catch (IOException e) {
            // This will only catch errors creating the log file itself.
            e.printStackTrace();
        }
    }
} 