package ar.com.oikosanalistas.admin.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static ar.com.oikosanalistas.admin.utils.StringUtils.isEmpty;

public class ValidatorUtils {

    private static final String PATTERN_EMAIL = "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@"
            + "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$";

    private static final String PATTERN_NOMBRE = "^([A-Za-z])+([ ]([A-Za-z])*)*$";

    private static final String PATTERN_PASSWORD = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).{6,20}$";

    private static final String PATTERN_FILE_NAME = "^([A-Za-z0-9])+([ ]([A-Za-z0-9])*)*$";

    private static final String DEFAULT_EMPTY_FILENAME = "noname";

    /**
     * Validate given email with regular expression.
     *
     * @param email email for validation
     * @return true valid email, otherwise false
     */
    public static boolean validarEmail(String email) {

        // Compiles the given regular expression into a pattern.
        Pattern pattern = Pattern.compile(PATTERN_EMAIL);

        // Match the given input against this pattern
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();

    }

    public static boolean validarNombre(String nombre) {
        // Compiles the given regular expression into a pattern.
        Pattern pattern = Pattern.compile(PATTERN_NOMBRE);

        // Match the given input against this pattern
        Matcher matcher = pattern.matcher(nombre);
        return matcher.matches();
    }

    public static boolean validarPassword(String pass) {
        // Compiles the given regular expression into a pattern.
        Pattern pattern = Pattern.compile(PATTERN_PASSWORD);

        // Match the given input against this pattern
        Matcher matcher = pattern.matcher(pass);
        return matcher.matches();
    }

    public static boolean validarFileName(String fiename) {
        // Compiles the given regular expression into a pattern.
        Pattern pattern = Pattern.compile(PATTERN_FILE_NAME);

        // Match the given input against this pattern
        Matcher matcher = pattern.matcher(fiename);
        return matcher.matches();
    }

    public static String curarString(String filename) {
        if (isEmpty(filename)) {
            return DEFAULT_EMPTY_FILENAME;
        }

        filename = filename.replace("/", "-");
        filename = filename.replace("\\", "-");

        return filename;
    }
}
