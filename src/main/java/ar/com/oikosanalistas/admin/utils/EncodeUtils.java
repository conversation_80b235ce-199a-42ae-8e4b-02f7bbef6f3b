package ar.com.oikosanalistas.admin.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;

public class EncodeUtils {

    public static String encode(String string) {
        try {
            String encoded = URLEncoder.encode(string, "UTF-8");
            encoded = encoded.replaceAll("\\.", "%2E");
            return encoded;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String decode(String string) {
        try {
            String decoded = string.replaceAll("%2E", ".");
            decoded = URLDecoder.decode(decoded, "UTF-8");
            return decoded;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }
}
