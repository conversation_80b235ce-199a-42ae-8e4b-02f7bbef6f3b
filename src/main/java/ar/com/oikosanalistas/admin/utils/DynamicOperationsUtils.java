package ar.com.oikosanalistas.admin.utils;

import ar.com.oikosanalistas.admin.MainLauncher;
import ar.com.oikosanalistas.admin.events.CheckVersionEvent;
import ar.com.oikosanalistas.admin.events.DynamicOperationsEvent;
import ar.com.oikosanalistas.admin.ui.model.pojo.DynamicOps;
import com.google.firebase.database.*;

import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.HashMap;
import java.util.Map;

import static ar.com.oikosanalistas.admin.utils.Constants.*;

public class DynamicOperationsUtils {

    private static DynamicOperationsUtils instance;

    private final String jarUrl;
    private Map<String, URLClassLoader> urlClassLoaderMap;

    public static DynamicOperationsUtils getInstance(String jarUrl) {
        if (instance == null) {
            instance = new DynamicOperationsUtils(jarUrl);
        }
        return instance;
    }

    private DynamicOperationsUtils(String jarUrl) {
        this.jarUrl = jarUrl;
    }

    public Class<?> getLocationOperation() throws MalformedURLException, ClassNotFoundException {
        return getMapScript(DYNAMIC_OPS_CLASS_LOCATION_OPERATION);
    }

    public Class<?> getHtmlPart1Operation() throws MalformedURLException, ClassNotFoundException {
        return getMapScript(DYNAMIC_OPS_CLASS_HTML_PART1_OPERATION);
    }

    public Class<?> getHtmlPart2Operation() throws MalformedURLException, ClassNotFoundException {
        return getMapScript(DYNAMIC_OPS_CLASS_HTML_PART2_OPERATION);
    }

    private Class<?> getMapScript(String className) throws MalformedURLException, ClassNotFoundException {
        getJarFromLinkOrCache(jarUrl);
        return urlClassLoaderMap.get(jarUrl).loadClass(className);
    }

    private void getJarFromLinkOrCache(String link) throws MalformedURLException {
        if (urlClassLoaderMap == null) {
            urlClassLoaderMap = new HashMap<>();
        }

        if (!urlClassLoaderMap.containsKey(link)) {
            URL[] classLoaderURLs = new URL[]{new URL(link)};
            URLClassLoader urlClassLoader = new URLClassLoader(classLoaderURLs);
            urlClassLoaderMap.put(link, urlClassLoader);
        }
    }
}
