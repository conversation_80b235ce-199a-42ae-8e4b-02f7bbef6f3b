package ar.com.oikosanalistas.admin.utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class StringUtils {

    /**
     * Recibe un string y decide si es vacío o no
     *
     * @param s string que se quiere saber si es vacio o no
     * @return true si el string es vacio, false en caso contrario
     */
    public static boolean isEmpty(String s) {
        return s == null || s.isEmpty();
    }

    public static boolean notEmpty(String... params) {
        boolean someEmpty = false;
        for (String s : params) {
            if (isEmpty(s)) {
                someEmpty = true;
            }
        }
        return someEmpty;
    }

    public static String formatHour(String horaAsMillis) {
        try {
            Long horaMillis = Long.valueOf(horaAsMillis);
            Date date = new Date(horaMillis);
            DateFormat formatter = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
            return formatter.format(date);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static boolean isANumber(String number) {
        String regex = "\\d+";
        return number.matches(regex);
    }
}
