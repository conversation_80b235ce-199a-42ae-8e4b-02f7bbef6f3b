package ar.com.oikosanalistas.admin.events;

import ar.com.oikosanalistas.admin.events.base.EventResult;
import com.google.firebase.auth.UserRecord;

public class CreateUserEvent extends EventResult<UserRecord> {

    public CreateUserEvent(UserRecord result) {
        super(result);
    }

    public CreateUserEvent(String errorMessage, Throwable throwable) {
        super(errorMessage, throwable);
    }

}
