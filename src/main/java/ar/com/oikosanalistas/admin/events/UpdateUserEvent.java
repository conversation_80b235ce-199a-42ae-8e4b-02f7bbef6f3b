package ar.com.oikosanalistas.admin.events;

import ar.com.oikosanalistas.admin.events.base.EventResult;
import com.google.firebase.auth.UserRecord;

public class UpdateUserEvent extends EventResult<UserRecord> {

    public UpdateUserEvent(UserRecord result) {
        super(result);
    }

    public UpdateUserEvent(String errorMessage, Throwable throwable) {
        super(errorMessage, throwable);
    }
}
