package ar.com.oikosanalistas.admin.events;

import ar.com.oikosanalistas.admin.events.base.EventResult;
import ar.com.oikosanalistas.admin.ui.model.pojo.Usuario;
import ar.com.oikosanalistas.admin.utils.Pair;

import java.util.List;

public class ListAllUsersEncuestaEvent extends EventResult<Pair<List<Usuario>, String>> {

    public ListAllUsersEncuestaEvent(Pair<List<Usuario>,String> result) {
        super(result);
    }

    public ListAllUsersEncuestaEvent(String errorMessage, Throwable throwable) {
        super(errorMessage, throwable);
    }
}
