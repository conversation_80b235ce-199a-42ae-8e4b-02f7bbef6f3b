package ar.com.oikosanalistas.admin.events.base;

public abstract class EventResult<T> {

    private boolean isSuccess;
    private T result;
    private String errorMessage;
    private Throwable throwable;

    public EventResult(T result) {
        this.result = result;
        isSuccess = true;
    }

    public EventResult(String errorMessage, Throwable throwable) {
        this.errorMessage = errorMessage;
        this.throwable = throwable;
        isSuccess = false;
    }

    public T getResult() {
        return result;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public Throwable getThrowable() {
        return throwable;
    }

    public boolean isSuccess() {
        return isSuccess;
    }
}
