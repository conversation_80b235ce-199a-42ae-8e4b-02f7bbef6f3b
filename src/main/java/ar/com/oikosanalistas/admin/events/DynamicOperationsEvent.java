package ar.com.oikosanalistas.admin.events;

import ar.com.oikosanalistas.admin.events.base.EventResult;
import ar.com.oikosanalistas.admin.ui.model.pojo.DynamicOps;
import ar.com.oikosanalistas.admin.ui.model.pojo.LastVersion;

public class DynamicOperationsEvent extends EventResult<DynamicOps> {

    public DynamicOperationsEvent(DynamicOps result){ super(result); }

    public DynamicOperationsEvent(String errorMessage, Throwable throwable) { super(errorMessage, throwable); }

}