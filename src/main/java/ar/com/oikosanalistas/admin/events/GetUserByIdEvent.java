package ar.com.oikosanalistas.admin.events;

import ar.com.oikosanalistas.admin.events.base.EventResult;
import com.google.firebase.auth.UserRecord;

public class GetUserByIdEvent extends EventResult<UserRecord> {

    public GetUserByIdEvent(UserRecord result) {
        super(result);
    }

    public GetUserByIdEvent(String errorMessage, Throwable throwable) {
        super(errorMessage, throwable);
    }
}
